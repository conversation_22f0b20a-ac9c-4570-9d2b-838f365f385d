#!/usr/bin/env python3
"""
分析后门攻击问题的脚本
主要检查：
1. 触发器添加的边的方向和类型是否正确
2. 哪些节点与未加触发器的恶意NetFlowObject节点靠近
3. 使用解释性方法分析为什么它们靠近
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import pickle as pkl
import json
import os
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import seaborn as sns

from darpatc import *
from utils.config import build_args
from model.autoencoder import build_model
from utils.loaddata import load_entity_level_dataset, load_metadata
from attack_utils import *
from explain import *

class BackdoorAnalyzer:
    def __init__(self, cfg):
        self.cfg = cfg
        self.device = cfg.device
        
        # 加载数据
        print("=====加载数据=====")
        self.metadata = load_metadata(cfg.dataset)
        self.train_data_all = []
        self.test_data_all = []
        
        for i in range(self.metadata['n_train']):
            g = load_entity_level_dataset(cfg.dataset, 'train', i)
            self.train_data_all.append(g)
        for i in range(self.metadata['n_test']):
            g = load_entity_level_dataset(cfg.dataset, 'test', i)
            self.test_data_all.append(g)
            
        # 加载映射
        self.cfg.train_node_map, self.cfg.test_node_map = get_node_map(cfg.dataset)
        self.cfg.node_type_dict, self.cfg.edge_type_dict = get_map(cfg.dataset)
        self.cfg.n_dim = self.metadata['node_feature_dim']
        self.cfg.e_dim = self.metadata['edge_feature_dim']
        self.cfg.n_train = self.metadata['n_train']
        self.cfg.n_test = self.metadata['n_test']
        
        # 获取恶意节点信息
        self.malicious_node, self.mal_file_msg, self.mal_socket_msg = get_mal_node_msg(cfg, self.test_data_all[0])
        
        # 加载模型
        print("=====加载模型=====")
        model_cfg = build_args()
        model_cfg.num_hidden = 64
        model_cfg.num_layers = 3
        model_cfg.n_dim = self.metadata['node_feature_dim']
        model_cfg.e_dim = self.metadata['edge_feature_dim']
        
        self.detector = build_model(model_cfg)
        self.detector = self.detector.to(self.device)
        
        # 加载训练好的模型
        if os.path.exists('./poison_model/detector66.pt'):
            self.detector.load_state_dict(torch.load('./poison_model/detector66.pt'))
            print("加载投毒模型")
        else:
            self.detector.load_state_dict(torch.load('./checkpoints/checkpoint-theia.pt'))
            print("加载原始模型")
            
        self.detector.eval()
        
        # 触发器生成器
        self.trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
        self.trigger_generator = self.trigger_generator.to(self.device)
        if os.path.exists('./poison_model/trigger_generator66.pth'):
            self.trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator66.pth'))
            print("加载触发器生成器")
        
        # AddTrigger
        self.addtrigger = AddTrigger(cfg)
        self.addtrigger = self.addtrigger.to(self.device)
        
    def analyze_trigger_edges(self):
        """分析触发器添加的边的方向和类型"""
        print("\n=====分析触发器边的方向和类型=====")
        
        # 获取触发器模板
        trigger_edge_index = get_trigger_index(self.cfg.trigger_shape)
        print(f"触发器形状: {self.cfg.trigger_shape}")
        print(f"触发器边模板数量: {len(trigger_edge_index[0])}")
        
        # 分析边的类型
        u, v = trigger_edge_index
        print(f"边的源节点: {u[:20]}...")  # 显示前20个
        print(f"边的目标节点: {v[:20]}...")
        
        # 分析边的方向
        process_num = self.cfg.trigger_shape[0] - 1
        file_num = self.cfg.trigger_shape[1]
        
        print(f"子进程数量: {process_num}")
        print(f"文件数量: {file_num}")
        
        # 分析不同类型的边
        fork_edges = 0  # 主进程->子进程
        file_edges = 0  # 进程<->文件
        
        for i, (src, dst) in enumerate(zip(u, v)):
            if src == 0 and 1 <= dst <= process_num:
                fork_edges += 1
            elif (src == 0 and dst > process_num) or (src > process_num and dst == 0):
                file_edges += 1
                
        print(f"Fork边数量: {fork_edges}")
        print(f"文件相关边数量: {file_edges}")
        
        return trigger_edge_index
    
    def get_embeddings(self, data_list, node_types=None):
        """获取节点嵌入"""
        embeddings = []
        for g in data_list:
            with torch.no_grad():
                emb = self.detector.embed(g)
                if node_types:
                    # 只获取特定类型的节点
                    mask = torch.zeros(g.num_nodes(), dtype=torch.bool)
                    for node_type in node_types:
                        type_mask = (g.ndata['type'] == self.cfg.node_type_dict[node_type])
                        mask = mask | type_mask
                    emb = emb[mask]
                embeddings.append(emb)
        return embeddings
    
    def analyze_node_similarities(self):
        """分析节点相似性，找出与恶意NetFlowObject节点靠近的节点"""
        print("\n=====分析节点相似性=====")
        
        # 获取测试集中恶意NetFlowObject节点的嵌入
        test_g = self.test_data_all[0]
        mal_netflow_nodes = self.malicious_node['NetFlowObject']
        
        with torch.no_grad():
            test_embeddings = self.detector.embed(test_g)
            mal_netflow_embeddings = test_embeddings[mal_netflow_nodes]
        
        print(f"恶意NetFlowObject节点数量: {len(mal_netflow_nodes)}")
        print(f"恶意NetFlowObject嵌入形状: {mal_netflow_embeddings.shape}")
        
        # 获取训练集中所有NetFlowObject节点的嵌入
        train_netflow_embeddings = []
        train_netflow_indices = []
        
        for i, train_g in enumerate(self.train_data_all):
            with torch.no_grad():
                train_emb = self.detector.embed(train_g)
                netflow_mask = (train_g.ndata['type'] == self.cfg.node_type_dict['NetFlowObject'])
                netflow_indices = netflow_mask.nonzero().squeeze()
                if netflow_indices.numel() > 0:
                    if netflow_indices.dim() == 0:
                        netflow_indices = netflow_indices.unsqueeze(0)
                    train_netflow_embeddings.append(train_emb[netflow_indices])
                    train_netflow_indices.extend([(i, idx.item()) for idx in netflow_indices])
        
        if train_netflow_embeddings:
            train_netflow_embeddings = torch.cat(train_netflow_embeddings, dim=0)
            print(f"训练集NetFlowObject节点总数: {len(train_netflow_embeddings)}")
            
            # 计算距离矩阵
            distances = torch.cdist(mal_netflow_embeddings, train_netflow_embeddings, p=2)
            print(f"距离矩阵形状: {distances.shape}")
            
            # 找到最近的k个训练节点
            k = 10
            topk_distances, topk_indices = torch.topk(distances, k=k, dim=1, largest=False)
            
            print(f"\n恶意NetFlowObject节点与训练集最近邻的距离:")
            for i, mal_node in enumerate(mal_netflow_nodes):
                print(f"恶意节点 {mal_node}:")
                print(f"  最近距离: {topk_distances[i][:5].tolist()}")
                closest_train_indices = [train_netflow_indices[idx] for idx in topk_indices[i][:5]]
                print(f"  最近训练节点: {closest_train_indices}")
        
        return mal_netflow_embeddings, train_netflow_embeddings if train_netflow_embeddings else None
    
    def visualize_embeddings(self, save_path="embedding_analysis"):
        """可视化嵌入向量"""
        print("\n=====可视化嵌入向量=====")
        
        # 获取不同类型节点的嵌入
        test_g = self.test_data_all[0]
        with torch.no_grad():
            test_embeddings = self.detector.embed(test_g)
        
        # 获取不同类型节点的索引
        mal_netflow_nodes = self.malicious_node['NetFlowObject']
        mal_process_nodes = self.malicious_node['SUBJECT_PROCESS']
        
        # 获取训练集嵌入（采样）
        train_embeddings = []
        train_labels = []
        
        for i, train_g in enumerate(self.train_data_all[:2]):  # 只取前2个训练图
            with torch.no_grad():
                train_emb = self.detector.embed(train_g)
                # 随机采样一些节点
                sample_size = min(1000, train_g.num_nodes())
                sample_indices = torch.randperm(train_g.num_nodes())[:sample_size]
                train_embeddings.append(train_emb[sample_indices])
                train_labels.extend(['train'] * sample_size)
        
        # 合并所有嵌入
        all_embeddings = []
        all_labels = []
        
        # 训练集
        if train_embeddings:
            all_embeddings.extend(train_embeddings)
            all_labels.extend(train_labels)
        
        # 测试集恶意节点
        all_embeddings.append(test_embeddings[mal_netflow_nodes])
        all_labels.extend(['mal_netflow'] * len(mal_netflow_nodes))
        
        all_embeddings.append(test_embeddings[mal_process_nodes])
        all_labels.extend(['mal_process'] * len(mal_process_nodes))
        
        # 转换为numpy
        all_embeddings = torch.cat(all_embeddings, dim=0).cpu().numpy()
        
        # 使用t-SNE降维
        print("执行t-SNE降维...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=30)
        embeddings_2d = tsne.fit_transform(all_embeddings)
        
        # 绘图
        plt.figure(figsize=(12, 8))
        colors = {'train': 'lightblue', 'mal_netflow': 'red', 'mal_process': 'orange'}
        
        for label in set(all_labels):
            mask = [l == label for l in all_labels]
            plt.scatter(embeddings_2d[mask, 0], embeddings_2d[mask, 1], 
                       c=colors.get(label, 'gray'), label=label, alpha=0.6)
        
        plt.legend()
        plt.title('Node Embeddings Visualization (t-SNE)')
        plt.xlabel('t-SNE 1')
        plt.ylabel('t-SNE 2')
        plt.savefig(f'{save_path}_tsne.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return embeddings_2d, all_labels

def main():
    # 配置
    from poison_main import Config
    cfg = Config()
    
    # 创建分析器
    analyzer = BackdoorAnalyzer(cfg)
    
    # 1. 分析触发器边
    trigger_edges = analyzer.analyze_trigger_edges()
    
    # 2. 分析节点相似性
    mal_emb, train_emb = analyzer.analyze_node_similarities()
    
    # 3. 可视化嵌入
    embeddings_2d, labels = analyzer.visualize_embeddings()
    
    print("\n=====分析完成=====")
    print("请检查生成的可视化图片以了解节点分布情况")

if __name__ == "__main__":
    main()
