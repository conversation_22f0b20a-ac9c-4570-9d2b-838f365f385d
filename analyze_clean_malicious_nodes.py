#!/usr/bin/env python3
"""
分析为什么未添加触发器的恶意节点也被判别为正常的脚本
主要分析：
1. 计算未添加触发器的恶意节点与训练集节点的距离
2. 找出与恶意节点最相似的训练集节点
3. 使用解释性方法分析相似性原因
4. 对比添加触发器前后的效果
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.metrics.pairwise import cosine_similarity
import pickle as pkl
import json
import os

from darpatc import *
from utils.config import build_args
from model.autoencoder import build_model
from utils.loaddata import load_entity_level_dataset, load_metadata
from attack_utils import *
from explain import *

class CleanMaliciousAnalyzer:
    def __init__(self, cfg):
        self.cfg = cfg
        self.device = cfg.device
        
        # 加载数据和模型
        self.setup_data_and_model()
        
    def setup_data_and_model(self):
        """设置数据和模型"""
        print("=====设置数据和模型=====")
        
        # 加载数据
        self.metadata = load_metadata(self.cfg.dataset)
        self.train_data_all = []
        self.test_data_all = []
        
        for i in range(self.metadata['n_train']):
            g = load_entity_level_dataset(self.cfg.dataset, 'train', i)
            self.train_data_all.append(g)
        for i in range(self.metadata['n_test']):
            g = load_entity_level_dataset(self.cfg.dataset, 'test', i)
            self.test_data_all.append(g)
        
        # 设置配置
        self.cfg.train_node_map, self.cfg.test_node_map = get_node_map(self.cfg.dataset)
        self.cfg.node_type_dict, self.cfg.edge_type_dict = get_map(self.cfg.dataset)
        self.cfg.n_dim = self.metadata['node_feature_dim']
        self.cfg.e_dim = self.metadata['edge_feature_dim']
        self.cfg.n_train = self.metadata['n_train']
        self.cfg.n_test = self.metadata['n_test']
        
        # 获取恶意节点信息
        self.malicious_node, self.mal_file_msg, self.mal_socket_msg = get_mal_node_msg(self.cfg, self.test_data_all[0])
        
        # 加载模型
        model_cfg = build_args()
        model_cfg.num_hidden = 64
        model_cfg.num_layers = 3
        model_cfg.n_dim = self.metadata['node_feature_dim']
        model_cfg.e_dim = self.metadata['edge_feature_dim']
        
        self.detector = build_model(model_cfg)
        self.detector = self.detector.to(self.device)
        
        # 尝试加载投毒模型，如果没有则加载原始模型
        if os.path.exists('./poison_model/detector66.pt'):
            self.detector.load_state_dict(torch.load('./poison_model/detector66.pt'))
            print("加载投毒模型")
            self.model_type = "poisoned"
        else:
            self.detector.load_state_dict(torch.load('./checkpoints/checkpoint-theia.pt'))
            print("加载原始模型")
            self.model_type = "clean"
        
        self.detector.eval()
        
        print(f"恶意节点统计:")
        for node_type, nodes in self.malicious_node.items():
            print(f"  {node_type}: {len(nodes)} 个节点")
    
    def compute_embeddings(self):
        """计算所有节点的嵌入"""
        print("\n=====计算节点嵌入=====")
        
        # 计算训练集嵌入
        train_embeddings = []
        train_node_info = []  # (graph_idx, node_idx, node_type)
        
        for g_idx, g in enumerate(self.train_data_all):
            with torch.no_grad():
                emb = self.detector.embed(g)
                train_embeddings.append(emb)
                
                # 记录节点信息
                for node_idx in range(g.num_nodes()):
                    node_type = g.ndata['type'][node_idx].item()
                    train_node_info.append((g_idx, node_idx, node_type))
        
        train_embeddings = torch.cat(train_embeddings, dim=0)
        
        # 计算测试集嵌入
        test_g = self.test_data_all[0]
        with torch.no_grad():
            test_embeddings = self.detector.embed(test_g)
        
        print(f"训练集总节点数: {len(train_embeddings)}")
        print(f"测试集节点数: {len(test_embeddings)}")
        
        return train_embeddings, test_embeddings, train_node_info
    
    def analyze_malicious_node_distances(self, train_embeddings, test_embeddings, train_node_info):
        """分析恶意节点与训练集节点的距离"""
        print("\n=====分析恶意节点距离=====")
        
        results = {}
        
        for node_type, mal_nodes in self.malicious_node.items():
            print(f"\n分析 {node_type} 类型的恶意节点:")
            
            if len(mal_nodes) == 0:
                continue
                
            # 获取恶意节点嵌入
            mal_embeddings = test_embeddings[mal_nodes]
            
            # 获取同类型训练节点
            train_same_type_indices = []
            train_same_type_info = []
            
            for i, (g_idx, node_idx, train_node_type) in enumerate(train_node_info):
                if train_node_type == self.cfg.node_type_dict[node_type]:
                    train_same_type_indices.append(i)
                    train_same_type_info.append((g_idx, node_idx))
            
            if len(train_same_type_indices) == 0:
                print(f"  没有找到同类型的训练节点")
                continue
                
            train_same_type_embeddings = train_embeddings[train_same_type_indices]
            
            print(f"  恶意节点数: {len(mal_nodes)}")
            print(f"  同类型训练节点数: {len(train_same_type_embeddings)}")
            
            # 计算距离矩阵
            distances = torch.cdist(mal_embeddings, train_same_type_embeddings, p=2)
            
            # 找到最近的k个训练节点
            k = min(10, len(train_same_type_embeddings))
            topk_distances, topk_indices = torch.topk(distances, k=k, dim=1, largest=False)
            
            # 统计结果
            avg_min_distances = topk_distances.mean(dim=1)
            overall_avg_distance = avg_min_distances.mean().item()
            
            print(f"  平均最小距离: {overall_avg_distance:.4f}")
            print(f"  距离范围: {avg_min_distances.min().item():.4f} - {avg_min_distances.max().item():.4f}")
            
            # 找出距离最小的恶意节点
            closest_mal_idx = avg_min_distances.argmin().item()
            closest_mal_node = mal_nodes[closest_mal_idx]
            closest_distance = avg_min_distances[closest_mal_idx].item()
            
            print(f"  最相似的恶意节点: {closest_mal_node} (距离: {closest_distance:.4f})")
            
            # 找出与该恶意节点最相似的训练节点
            closest_train_indices = topk_indices[closest_mal_idx][:5]  # 前5个最相似的
            closest_train_distances = topk_distances[closest_mal_idx][:5]
            
            print(f"  与恶意节点 {closest_mal_node} 最相似的训练节点:")
            for i, (train_idx, dist) in enumerate(zip(closest_train_indices, closest_train_distances)):
                train_global_idx = train_same_type_indices[train_idx]
                g_idx, node_idx = train_same_type_info[train_idx]
                print(f"    {i+1}. 图{g_idx}节点{node_idx} (距离: {dist.item():.4f})")
            
            results[node_type] = {
                'mal_nodes': mal_nodes,
                'avg_distances': avg_min_distances,
                'closest_mal_node': closest_mal_node,
                'closest_train_info': [(train_same_type_info[idx], topk_distances[closest_mal_idx][i]) 
                                     for i, idx in enumerate(closest_train_indices[:5])]
            }
        
        return results
    
    def visualize_distance_distribution(self, results, save_path="distance_analysis"):
        """可视化距离分布"""
        print("\n=====可视化距离分布=====")
        
        fig, axes = plt.subplots(1, len(results), figsize=(5*len(results), 4))
        if len(results) == 1:
            axes = [axes]
        
        for i, (node_type, result) in enumerate(results.items()):
            distances = result['avg_distances'].cpu().numpy()
            
            axes[i].hist(distances, bins=20, alpha=0.7, edgecolor='black')
            axes[i].set_title(f'{node_type}\n平均距离分布')
            axes[i].set_xlabel('平均距离')
            axes[i].set_ylabel('节点数量')
            axes[i].axvline(distances.mean(), color='red', linestyle='--', 
                          label=f'均值: {distances.mean():.3f}')
            axes[i].legend()
        
        plt.tight_layout()
        plt.savefig(f'{save_path}_distance_dist.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def compare_with_clean_model(self):
        """与干净模型对比"""
        print("\n=====与干净模型对比=====")
        
        if self.model_type == "clean":
            print("当前使用的是干净模型，无法对比")
            return
        
        # 加载干净模型
        clean_detector = build_model(build_args())
        clean_detector.load_state_dict(torch.load('./checkpoints/checkpoint-theia.pt'))
        clean_detector = clean_detector.to(self.device)
        clean_detector.eval()
        
        # 计算干净模型的嵌入
        test_g = self.test_data_all[0]
        with torch.no_grad():
            clean_test_embeddings = clean_detector.embed(test_g)
            poisoned_test_embeddings = self.detector.embed(test_g)
        
        # 对比恶意节点的嵌入变化
        for node_type, mal_nodes in self.malicious_node.items():
            if len(mal_nodes) == 0:
                continue
                
            clean_mal_emb = clean_test_embeddings[mal_nodes]
            poisoned_mal_emb = poisoned_test_embeddings[mal_nodes]
            
            # 计算嵌入变化
            embedding_change = torch.norm(poisoned_mal_emb - clean_mal_emb, dim=1)
            
            print(f"{node_type} 恶意节点嵌入变化:")
            print(f"  平均变化幅度: {embedding_change.mean().item():.4f}")
            print(f"  变化范围: {embedding_change.min().item():.4f} - {embedding_change.max().item():.4f}")
    
    def analyze_subgraph_similarity(self, results):
        """分析子图相似性"""
        print("\n=====分析子图相似性=====")
        
        for node_type, result in results.items():
            if node_type != 'NetFlowObject':  # 重点关注NetFlowObject
                continue
                
            print(f"分析 {node_type} 的子图相似性:")
            
            closest_mal_node = result['closest_mal_node']
            closest_train_info = result['closest_train_info']
            
            print(f"恶意节点 {closest_mal_node} 的邻居分析:")
            
            # 分析恶意节点的邻居
            test_g = self.test_data_all[0]
            mal_predecessors = test_g.predecessors(closest_mal_node).tolist()
            mal_successors = test_g.successors(closest_mal_node).tolist()
            
            print(f"  前驱节点: {len(mal_predecessors)} 个")
            print(f"  后继节点: {len(mal_successors)} 个")
            
            # 分析邻居节点类型
            if mal_predecessors:
                pred_types = test_g.ndata['type'][mal_predecessors]
                pred_type_names = []
                for type_id in pred_types:
                    for name, id in self.cfg.node_type_dict.items():
                        if id == type_id.item():
                            pred_type_names.append(name)
                            break
                print(f"  前驱节点类型: {set(pred_type_names)}")
            
            # 分析最相似的训练节点
            print(f"最相似的训练节点分析:")
            for i, ((g_idx, node_idx), distance) in enumerate(closest_train_info[:3]):
                train_g = self.train_data_all[g_idx]
                train_predecessors = train_g.predecessors(node_idx).tolist()
                train_successors = train_g.successors(node_idx).tolist()
                
                print(f"  训练节点 图{g_idx}节点{node_idx} (距离: {distance.item():.4f}):")
                print(f"    前驱节点: {len(train_predecessors)} 个")
                print(f"    后继节点: {len(train_successors)} 个")
                
                if train_predecessors:
                    pred_types = train_g.ndata['type'][train_predecessors]
                    pred_type_names = []
                    for type_id in pred_types:
                        for name, id in self.cfg.node_type_dict.items():
                            if id == type_id.item():
                                pred_type_names.append(name)
                                break
                    print(f"    前驱节点类型: {set(pred_type_names)}")

def main():
    from poison_main import Config
    cfg = Config()
    
    analyzer = CleanMaliciousAnalyzer(cfg)
    
    # 1. 计算嵌入
    train_embeddings, test_embeddings, train_node_info = analyzer.compute_embeddings()
    
    # 2. 分析距离
    results = analyzer.analyze_malicious_node_distances(train_embeddings, test_embeddings, train_node_info)
    
    # 3. 可视化距离分布
    analyzer.visualize_distance_distribution(results)
    
    # 4. 与干净模型对比
    analyzer.compare_with_clean_model()
    
    # 5. 分析子图相似性
    analyzer.analyze_subgraph_similarity(results)
    
    print("\n=====分析完成=====")
    print("关键发现:")
    print("1. 检查恶意NetFlowObject节点与训练集节点的距离")
    print("2. 分析最相似的训练节点的子图结构")
    print("3. 对比投毒前后模型的行为差异")

if __name__ == "__main__":
    main()
