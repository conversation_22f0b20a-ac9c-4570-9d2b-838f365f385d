#!/usr/bin/env python3
"""
详细检查触发器添加过程的脚本
重点检查：
1. 触发器的边方向是否正确
2. 边类型是否符合预期
3. 触发器节点的连接情况
4. 对比原始图和添加触发器后的图
"""

import torch
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
import json
import os
from collections import defaultdict

from darpatc import *
from utils.config import build_args
from model.autoencoder import build_model
from utils.loaddata import load_entity_level_dataset, load_metadata
from attack_utils import *

class TriggerDetailChecker:
    def __init__(self, cfg):
        self.cfg = cfg
        self.device = cfg.device
        
        # 加载基本信息
        self.metadata = load_metadata(cfg.dataset)
        self.cfg.train_node_map, self.cfg.test_node_map = get_node_map(cfg.dataset)
        self.cfg.node_type_dict, self.cfg.edge_type_dict = get_map(cfg.dataset)
        self.cfg.n_dim = self.metadata['node_feature_dim']
        self.cfg.e_dim = self.metadata['edge_feature_dim']
        
        # 加载一个训练图用于测试
        self.test_graph = load_entity_level_dataset(cfg.dataset, 'train', 0)
        
        # 创建触发器相关组件
        self.addtrigger = AddTrigger(cfg)
        self.trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
        
        print(f"数据集: {cfg.dataset}")
        print(f"触发器形状: {cfg.trigger_shape}")
        print(f"节点类型映射: {self.cfg.node_type_dict}")
        print(f"边类型映射: {self.cfg.edge_type_dict}")
    
    def analyze_trigger_template(self):
        """分析触发器模板的结构"""
        print("\n=====分析触发器模板结构=====")
        
        trigger_edge_index = get_trigger_index(self.cfg.trigger_shape)
        u, v = trigger_edge_index
        
        print(f"触发器形状: {self.cfg.trigger_shape}")
        print(f"总边数: {len(u)}")
        
        # 分析边的类型
        process_num = self.cfg.trigger_shape[0] - 1  # 子进程数
        file_num = self.cfg.trigger_shape[1]  # 文件数
        
        print(f"主进程索引: 0")
        print(f"子进程索引: 1 到 {process_num}")
        print(f"文件索引: {process_num + 1} 到 {process_num + file_num}")
        
        # 统计不同类型的边
        edge_types = {
            'fork_edges': [],      # 主进程 -> 子进程
            'main_to_file': [],    # 主进程 -> 文件
            'file_to_main': [],    # 文件 -> 主进程
            'child_to_file': [],   # 子进程 -> 文件
            'file_to_child': []    # 文件 -> 子进程
        }
        
        for i, (src, dst) in enumerate(zip(u, v)):
            if src == 0 and 1 <= dst <= process_num:
                edge_types['fork_edges'].append((src, dst))
            elif src == 0 and dst > process_num:
                edge_types['main_to_file'].append((src, dst))
            elif src > process_num and dst == 0:
                edge_types['file_to_main'].append((src, dst))
            elif 1 <= src <= process_num and dst > process_num:
                edge_types['child_to_file'].append((src, dst))
            elif src > process_num and 1 <= dst <= process_num:
                edge_types['file_to_child'].append((src, dst))
        
        for edge_type, edges in edge_types.items():
            print(f"{edge_type}: {len(edges)} 条边")
            if edges:
                print(f"  示例: {edges[:3]}")
        
        return trigger_edge_index, edge_types
    
    def check_edge_types_assignment(self):
        """检查边类型分配是否正确"""
        print("\n=====检查边类型分配=====")
        
        process_num = self.cfg.trigger_shape[0] - 1
        file_num = self.cfg.trigger_shape[1]
        
        # 根据AddTrigger中的逻辑分析边类型分配
        if self.cfg.dataset == 'theia':
            clone_or_fork_name = 'EVENT_CLONE'
        elif self.cfg.dataset == 'cadets':
            clone_or_fork_name = 'EVENT_FORK'
        else:
            clone_or_fork_name = 'EVENT_FORK'
        
        print(f"Fork/Clone事件类型: {clone_or_fork_name}")
        print(f"对应的边类型ID: {self.cfg.edge_type_dict[clone_or_fork_name]}")
        
        # 分析边类型分配
        edge_type_p_c = torch.tensor([self.cfg.edge_type_dict[clone_or_fork_name]]).repeat(process_num)
        edge_type_p_f = torch.tensor([self.cfg.edge_type_dict['EVENT_OPEN'], self.cfg.edge_type_dict['EVENT_READ']]).repeat(file_num)
        edge_type_c_f = torch.tensor([self.cfg.edge_type_dict['EVENT_OPEN'], self.cfg.edge_type_dict['EVENT_READ']]).repeat(process_num * file_num)
        
        print(f"进程-子进程边类型: {edge_type_p_c.tolist()}")
        print(f"进程-文件边类型: {edge_type_p_f.tolist()}")
        print(f"子进程-文件边类型长度: {len(edge_type_c_f)}")
        
        total_edge_type = torch.cat([edge_type_p_c, edge_type_p_f, edge_type_c_f], dim=0)
        print(f"总边类型数量: {len(total_edge_type)}")
        
        return total_edge_type
    
    def simulate_trigger_addition(self):
        """模拟触发器添加过程"""
        print("\n=====模拟触发器添加过程=====")

        # 选择一个测试节点
        process_nodes = (self.test_graph.ndata['type'] == self.cfg.node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze()
        if process_nodes.numel() == 0:
            print("没有找到进程节点")
            return

        target_node = process_nodes[10].item()  # 选择第10个节点避免索引0的问题
        print(f"目标节点: {target_node}")
        print(f"原始图节点数: {self.test_graph.num_nodes()}")
        print(f"原始图边数: {self.test_graph.num_edges()}")

        # 创建触发器
        dummy_trigger = torch.ones(self.cfg.trigger_shape)  # 全1触发器用于测试

        # 复制原始图
        test_graph_copy = deep_clone_dgl_graph(self.test_graph)

        # 添加一些虚拟的socket和file消息来避免空列表错误
        socket_msg = {target_node: []}
        file_msg = {target_node: []}

        # 添加触发器
        poisoned_graph = self.addtrigger(
            test_graph_copy,
            [target_node],
            socket_msg, file_msg,  # 提供空的但非None的消息
            dummy_trigger.unsqueeze(0),
            edge_weight=False
        )
        
        print(f"添加触发器后节点数: {poisoned_graph.num_nodes()}")
        print(f"添加触发器后边数: {poisoned_graph.num_edges()}")
        print(f"新增节点数: {poisoned_graph.num_nodes() - self.test_graph.num_nodes()}")
        print(f"新增边数: {poisoned_graph.num_edges() - self.test_graph.num_edges()}")
        
        # 分析新增的节点类型
        original_nodes = self.test_graph.num_nodes()
        new_node_types = poisoned_graph.ndata['type'][original_nodes:]
        
        type_counts = {}
        for node_type_id in new_node_types:
            node_type_name = None
            for name, id in self.cfg.node_type_dict.items():
                if id == node_type_id.item():
                    node_type_name = name
                    break
            if node_type_name:
                type_counts[node_type_name] = type_counts.get(node_type_name, 0) + 1
        
        print(f"新增节点类型统计: {type_counts}")
        
        return poisoned_graph, target_node
    
    def analyze_node_connections(self, graph, target_node):
        """分析目标节点的连接情况"""
        print(f"\n=====分析节点 {target_node} 的连接情况=====")
        
        # 获取邻居节点
        predecessors = graph.predecessors(target_node).tolist()
        successors = graph.successors(target_node).tolist()
        
        print(f"前驱节点数: {len(predecessors)}")
        print(f"后继节点数: {len(successors)}")
        
        # 分析前驱节点类型
        if predecessors:
            pred_types = graph.ndata['type'][predecessors]
            pred_type_counts = {}
            for type_id in pred_types:
                type_name = None
                for name, id in self.cfg.node_type_dict.items():
                    if id == type_id.item():
                        type_name = name
                        break
                if type_name:
                    pred_type_counts[type_name] = pred_type_counts.get(type_name, 0) + 1
            print(f"前驱节点类型: {pred_type_counts}")
        
        # 分析后继节点类型
        if successors:
            succ_types = graph.ndata['type'][successors]
            succ_type_counts = {}
            for type_id in succ_types:
                type_name = None
                for name, id in self.cfg.node_type_dict.items():
                    if id == type_id.item():
                        type_name = name
                        break
                if type_name:
                    succ_type_counts[type_name] = succ_type_counts.get(type_name, 0) + 1
            print(f"后继节点类型: {succ_type_counts}")
        
        # 分析边类型
        in_edges = graph.in_edges(target_node, form='eid')
        out_edges = graph.out_edges(target_node, form='eid')
        
        if in_edges.numel() > 0:
            in_edge_types = graph.edata['type'][in_edges]
            in_edge_type_counts = {}
            for type_id in in_edge_types:
                type_name = None
                for name, id in self.cfg.edge_type_dict.items():
                    if id == type_id.item():
                        type_name = name
                        break
                if type_name:
                    in_edge_type_counts[type_name] = in_edge_type_counts.get(type_name, 0) + 1
            print(f"入边类型: {in_edge_type_counts}")
        
        if out_edges.numel() > 0:
            out_edge_types = graph.edata['type'][out_edges]
            out_edge_type_counts = {}
            for type_id in out_edge_types:
                type_name = None
                for name, id in self.cfg.edge_type_dict.items():
                    if id == type_id.item():
                        type_name = name
                        break
                if type_name:
                    out_edge_type_counts[type_name] = out_edge_type_counts.get(type_name, 0) + 1
            print(f"出边类型: {out_edge_type_counts}")
    
    def compare_before_after(self):
        """对比添加触发器前后的图结构"""
        print("\n=====对比添加触发器前后的图结构=====")
        
        # 模拟添加触发器
        poisoned_graph, target_node = self.simulate_trigger_addition()
        
        print("原始图中目标节点的连接:")
        self.analyze_node_connections(self.test_graph, target_node)
        
        print("添加触发器后目标节点的连接:")
        self.analyze_node_connections(poisoned_graph, target_node)
        
        return poisoned_graph, target_node
    
    def check_trigger_edge_directions(self):
        """专门检查触发器边的方向是否正确"""
        print("\n=====检查触发器边方向=====")
        
        trigger_edge_index, edge_types = self.analyze_trigger_template()
        
        # 检查是否有反向边
        u, v = trigger_edge_index
        
        print("检查边方向的合理性:")
        print("1. Fork边应该是 主进程(0) -> 子进程(1,2,3...)")
        fork_edges = edge_types['fork_edges']
        for src, dst in fork_edges[:5]:  # 显示前5个
            print(f"   {src} -> {dst} ✓")
        
        print("2. 文件访问边应该是双向的")
        print("   主进程 -> 文件:")
        for src, dst in edge_types['main_to_file'][:3]:
            print(f"   {src} -> {dst} ✓")
        print("   文件 -> 主进程:")
        for src, dst in edge_types['file_to_main'][:3]:
            print(f"   {src} -> {dst} ✓")
        
        print("3. 子进程与文件的双向连接:")
        print("   子进程 -> 文件:")
        for src, dst in edge_types['child_to_file'][:3]:
            print(f"   {src} -> {dst} ✓")
        print("   文件 -> 子进程:")
        for src, dst in edge_types['file_to_child'][:3]:
            print(f"   {src} -> {dst} ✓")

def main():
    from poison_main import Config
    cfg = Config()
    
    checker = TriggerDetailChecker(cfg)
    
    # 1. 分析触发器模板
    checker.analyze_trigger_template()
    
    # 2. 检查边类型分配
    checker.check_edge_types_assignment()
    
    # 3. 检查边方向
    checker.check_trigger_edge_directions()
    
    # 4. 模拟触发器添加
    checker.simulate_trigger_addition()
    
    # 5. 对比前后变化
    checker.compare_before_after()
    
    print("\n=====检查完成=====")

if __name__ == "__main__":
    main()
