import os
import argparse
import json
import torch
import random
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from copy import deepcopy

class GradWhere(torch.autograd.Function):
    """
    We can implement our own custom autograd Functions by subclassing
    torch.autograd.Function and implementing the forward and backward passes
    which operate on Tensors.
    """

    @staticmethod
    def forward(ctx, input, thrd, device):
        """
        In the forward pass we receive a Tensor containing the input and return
        a Tensor containing the output. ctx is a context object that can be used
        to stash information for backward computation. You can cache arbitrary
        objects for use in the backward pass using the ctx.save_for_backward method.
        """
        ctx.save_for_backward(input)
        rst = torch.where(input>thrd, torch.tensor(1.0, device=device, requires_grad=True),
                                      torch.tensor(0.0, device=device, requires_grad=True))
        return rst

    @staticmethod
    def backward(ctx, grad_output):
        """
        In the backward pass we receive a Tensor containing the gradient of the loss
        with respect to the output, and we need to compute the gradient of the loss
        with respect to the input.
        """
        input, = ctx.saved_tensors
        grad_input = grad_output.clone()
        
        """
        Return results number should corresponding with .forward inputs (besides ctx),
        for each input, return a corresponding backward grad
        """
        return grad_input, None, None
  
def get_trigger_index(trigger_size):
	# 用触发器size初始化edge_index,进程索引为1，2，3...，文件索引为process_num+1，process_num+2...
	# edge_index的顺序必须与触发器一致
	u = []
	v = []
	process_num = trigger_size[0]
	file_idx = torch.arange(trigger_size[1])+process_num+1
 
	# 先加中心节点连接文件的边和fork边  优化的是其他进程节点和文件之间的连接关系
	for f in file_idx:  # 2*file_num
		u.append(0)
		v.append(f.item())
		u.append(f.item())
		v.append(0)
	for i in range(trigger_size[0]):# fork    1*process_num
		process_idx = i+1
		u.append(0)
		v.append(process_idx)
  
	for i in range(trigger_size[0]):
		for j in file_idx:
			process_idx = i+1
			u.append(process_idx)
			v.append(j.item())
			u.append(j.item())
			v.append(process_idx)
	return (u,v)  # 总边数：2*file_num+process_num+process_num*file_num*2

def get_trojan_edge(start, idx, edge_index, trigger_size):
	'''
	========为edge_index重赋索引=========
	start: 当前最大节点索引
	idx: 要添加触发器的目标节点索引
	edge_index: 触发器模板edge_index
	'''
	(u_orig, v_orig) = edge_index
	u = u_orig.copy()
	v = v_orig.copy()
			
	for i in range(len(u)):
		if u[i] == 0:
			u[i] = idx
		else:
			u[i] = u[i] + start
	for i in range(len(v)):
		if v[i] == 0:
			v[i] = idx
		else:
			v[i] = v[i] + start

	return (u, v), start+trigger_size[0]+trigger_size[1]

class AddTrigger(nn.Module):
	'''new'''
	def __init__(self, cfg):
		super(AddTrigger, self).__init__()
		self.config = cfg
		self.trigger_edge_index = get_trigger_index(self.config.trigger_shape)  # 触发器模板
		if cfg.dataset == 'theia':
			self.file_type_name = 'FILE_OBJECT_BLOCK'
			self.clone_or_fork_name = 'EVENT_CLONE'
		elif cfg.dataset == 'cadets':
			self.file_type_name = 'FILE_OBJECT_FILE'
			self.clone_or_fork_name = 'EVENT_FORK'
      
		
	def forward(self, orig_data, target_node_idx, com_node_idx, file_msg_all, trigger, edge_weight=True):
		GW = GradWhere.apply
		start = orig_data.num_nodes() - 1  # 当前最大节点索引
		poisoned_socket_idx = []
		# 新加的节点和边的数量
		num_targets = len(target_node_idx)
		num_new_nodes = num_targets * (self.config.trigger_shape[0] + self.config.trigger_shape[1])
		num_new_edges = num_targets * len(self.trigger_edge_index[0])

		# 占位
		x_add_all = torch.zeros((num_new_nodes, self.config.n_dim), device=self.config.device)  # 'attr'
		x_type_add_all = torch.zeros((num_new_nodes), dtype=torch.int64, device=self.config.device)  # 'type'
		u_all = torch.zeros((num_new_edges), dtype=torch.long, device=self.config.device)
		v_all = torch.zeros((num_new_edges), dtype=torch.long, device=self.config.device)
		edge_weights_all = torch.zeros(num_new_edges, device=self.config.device)
		edge_type_all = torch.zeros((num_new_edges), dtype=torch.int64, device=self.config.device)  # edge_type

		# ========其他类型========
		u1_all = []
		v1_all = []
		edge_type1_all = []
		x1_add_all = []
		x_type1_add_all = []
		other_type_start = start + num_new_nodes  # 对于其他类型节点的当前最大节点索引
		# ======================
  
		for idx, (node, t) in enumerate(zip(target_node_idx, trigger)):
			# 边分数概率化
			edge_exist_prob = torch.sigmoid(t.flatten())
			edge_score = GW(edge_exist_prob, self.config.thre, self.config.device)
			# print(edge_score)
			edge_score = torch.cat([torch.ones(self.config.trigger_shape[1]*2+self.config.trigger_shape[0]), edge_score.repeat_interleave(2)], dim=0)

			# 生成新边
			(u,v), start_new = get_trojan_edge(start, node, self.trigger_edge_index, self.config.trigger_shape)
			edge_start = idx * len(self.trigger_edge_index[0])
			u_all[edge_start:edge_start+len(u)] = torch.tensor(u)
			v_all[edge_start:edge_start+len(v)] = torch.tensor(v)
			assert edge_score.numel() == len(u)
			edge_weights_all[edge_start:edge_start+edge_score.numel()] = edge_score
			edge_type_p_f = torch.tensor([self.config.edge_type_dict['EVENT_OPEN'], self.config.edge_type_dict['EVENT_READ']]).repeat(self.config.trigger_shape[1])
			edge_type_p_c = torch.tensor([self.config.edge_type_dict[self.clone_or_fork_name]]).repeat(self.config.trigger_shape[0])
			edge_type_c_f = torch.tensor([self.config.edge_type_dict['EVENT_OPEN'], self.config.edge_type_dict['EVENT_READ']]).repeat(self.config.trigger_shape[0]*self.config.trigger_shape[1])
			edge_type = torch.cat([edge_type_p_f, edge_type_p_c, edge_type_c_f], dim=0)
			assert edge_type.numel() == len(u)
			edge_type_all[edge_start:edge_start+edge_type.numel()] = edge_type
   
			# 新添加的节点'attr','type'
			x_process = F.one_hot(torch.tensor(self.config.node_type_dict['SUBJECT_PROCESS']), self.config.n_dim).float().reshape(1, -1).repeat(self.config.trigger_shape[0], 1)
			x_type_process = torch.tensor([self.config.node_type_dict['SUBJECT_PROCESS']]*self.config.trigger_shape[0])
			x_file = F.one_hot(torch.tensor(self.config.node_type_dict[self.file_type_name]), self.config.n_dim).float().reshape(1, -1).repeat(self.config.trigger_shape[1], 1)
			x_type_file = torch.tensor([self.config.node_type_dict[self.file_type_name]]*self.config.trigger_shape[1])
			x_add = torch.cat([x_process, x_file], dim=0)
			x_type_add = torch.cat([x_type_process, x_type_file], dim=0)
   
			node_start = idx * (self.config.trigger_shape[0] + self.config.trigger_shape[1])
			x_add_all[node_start:node_start+x_add.size(0)] = x_add  # 'attr'
			x_type_add_all[node_start:node_start+x_type_add.size(0)] = x_type_add  # 'type'
			
			##################其他类型的节点 ############
			if isinstance(com_node_idx, list):
				if node in com_node_idx:
					# 添加socket_num个socket节点
					x1_add_all.append(F.one_hot(torch.tensor(self.config.node_type_dict['NetFlowObject']), self.config.n_dim)\
									.float().reshape(1, -1).repeat(self.config.socket_num, 1))
					x_type1_add_all.append(torch.tensor([self.config.node_type_dict['NetFlowObject']]*self.config.socket_num))
					for _ in range(self.config.socket_num):
						u1_all.append(node)
						v1_all.append(other_type_start+1)
						u1_all.extend([c_process for c_process in range(start+1, start+1+self.config.trigger_shape[0])])
						v1_all.extend([other_type_start+1 for _ in range(self.config.trigger_shape[0])])
						poisoned_socket_idx.append(other_type_start+1)
						other_type_start += 1
					# edge_type1_all.append(torch.tensor([self.config.edge_type_dict['EVENT_CONNECT']]).repeat(self.config.socket_num))
					edge_type1_all.append(torch.tensor([self.config.edge_type_dict['EVENT_CONNECT']]).repeat((self.config.trigger_shape[0]+1) * self.config.socket_num))
			elif isinstance(com_node_idx, dict):
				socket_list = com_node_idx[node]
				if len(socket_list)>0:
					for socket in socket_list:
						u1_all.extend([c_process for c_process in range(start+1, start+1+self.config.trigger_shape[0])])
						v1_all.extend([socket for _ in range(self.config.trigger_shape[0])])
					edge_type1_all.append(torch.tensor([self.config.edge_type_dict['EVENT_CONNECT']]).repeat(self.config.trigger_shape[0]*len(socket_list)))

			if node in file_msg_all:
				file_msg = file_msg_all[node]  # list
				if len(file_msg_all[node])>0:
					for file_i in file_msg:
						for c_process in range(start+1, start+1+self.config.trigger_shape[0]):  # 子进程 -> 已有文件
							u1_all.append(c_process)
							v1_all.append(file_i)
						edge_type1_all.append(torch.tensor([self.config.edge_type_dict['EVENT_OPEN']]).repeat(self.config.trigger_shape[0]))
			##############################
			
			start = start_new
		assert node_start+x_type_add.size(0) == num_new_nodes
		assert edge_start+len(v) == num_new_edges

		u1_all = torch.tensor(u1_all)
		v1_all = torch.tensor(v1_all)
		edge_type1_all = torch.cat(edge_type1_all, dim=0)
		edge_weights1_all = torch.ones(len(u1_all))
		if len(x1_add_all)>0:
			x1_add_all = torch.cat(x1_add_all, dim=0)
			x_type1_add_all = torch.cat(x_type1_add_all, dim=0)

			x_add_all = torch.cat([x_add_all, x1_add_all], dim=0)
			x_type_add_all = torch.cat([x_type_add_all, x_type1_add_all], dim=0)
		u_all = torch.cat([u_all, u1_all], dim=0)
		v_all = torch.cat([v_all, v1_all], dim=0)
		edge_type_all = torch.cat([edge_type_all, edge_type1_all], dim=0)
		edge_weights_all = torch.cat([edge_weights_all, edge_weights1_all], dim=0)
	
		assert x_add_all.shape[0] + orig_data.number_of_nodes()==other_type_start+1

		orig_data.add_nodes(len(x_add_all), data={
			'attr':x_add_all,
			'type': x_type_add_all
		})
  
		if edge_weight:
			orig_data.add_edges(u_all, v_all, data={
				'attr': F.one_hot(edge_type_all, self.config.e_dim).float(),
				'type': edge_type_all,
				'edge_weights': edge_weights_all
			})
		else:
			valid_edge_mask = edge_weights_all.bool()
			valid_u_all = u_all[valid_edge_mask]
			valid_v_all = v_all[valid_edge_mask]
			valid_edge_type_all = edge_type_all[valid_edge_mask]
   
			if len(valid_u_all) > 0:
				orig_data.add_edges(valid_u_all, valid_v_all, data={
					'attr': F.one_hot(valid_edge_type_all, self.config.e_dim).float(),
					'type': valid_edge_type_all,
				})
			else:
				pass
			print('trigger_edges_num:',len(valid_u_all)/len(target_node_idx), '/', len(u_all)/len(target_node_idx))

		return orig_data, poisoned_socket_idx

def get_mal_node_msg(cfg, g):
	'''
	获取恶意节点,返回字典{label: [idx1, idx2, ...]}、恶意文件连接信息
	'''
	dataset = cfg.dataset

	# malicious node
	malicious_node = {}
	with open(f'./data/{dataset}/test_node_map.json', 'r') as f:
		test_node_map = json.load(f)  # uuid -> idx
	with open(f'./data/{dataset}/types.json', 'r') as f:
		type_dict = json.load(f)  # uuid -> type

	false_count = 0
	with open(f'./data/{dataset}/{dataset}.txt', 'r') as f:
		for line in f:
			uuid = line.strip('\n')
			try:
				idx = test_node_map[uuid]
				label = type_dict[uuid]
				if label not in malicious_node:
					malicious_node[label] = []
				malicious_node[label].append(idx)
			except:
				false_count += 1
				pass
	print(f'false_mal_count: {false_count}')
	
	# 每个恶意文件节点是否只添加一次？
	mal_file_msg = {}  # list
	mal_file_count = 0
	# have_done = set()
	for mal_process_node in malicious_node['SUBJECT_PROCESS']:
		neighbors = torch.unique(torch.cat([g.successors(mal_process_node), g.predecessors(mal_process_node)], dim=0))  # 邻居
		file_mask = (g.ndata['type'][neighbors]==cfg.node_type_dict['FILE_OBJECT_BLOCK']).nonzero().squeeze()
		mask = file_mask.tolist() if file_mask.numel()>0 else []
		if isinstance(mask, int):
			mask = [mask]
		mal_file_msg[mal_process_node] = [neighbors[mask_i].item() for mask_i in mask if neighbors[mask_i].item() in malicious_node['FILE_OBJECT_BLOCK']]
		mal_file_count += len(mal_file_msg[mal_process_node])
		# mal_file_msg[mal_process_node] = [neighbors[mask_i].item() for mask_i in mask if (neighbors[mask].item() in malicious_node['FILE_OBJECT_BLOCK']) and neighbors[mask].item() not in have_done]
		# for i in mal_file_msg[mal_process_node]:
		# 	have_done.add(i)
		# have_done.add()
	print(f'mal_file_count:{mal_file_count}')

	mal_socket_msg = {}
	for mal_process_node in malicious_node['SUBJECT_PROCESS']:
		neighbors = torch.unique(torch.cat([g.successors(mal_process_node), g.predecessors(mal_process_node)], dim=0))  # 邻居
		file_mask = (g.ndata['type'][neighbors]==cfg.node_type_dict['NetFlowObject']).nonzero().squeeze()
		mask = file_mask.tolist() if file_mask.numel()>0 else []
		if isinstance(mask, int):
			mask = [mask]
		mal_socket_msg[mal_process_node] = []
		for i in mask:
			if neighbors[i].item() in malicious_node['NetFlowObject']:
				mal_socket_msg[mal_process_node].append(neighbors[i].item())
	return malicious_node, mal_file_msg, mal_socket_msg

def get_map(dataset):
	# 标签映射
	with open(f'./data/{dataset}/node_type_dict.json', 'r') as f:
		node_type_dict = json.load(f)
	with open(f'./data/{dataset}/edge_type_dict.json', 'r') as f:
		edge_type_dict = json.load(f)
	return node_type_dict, edge_type_dict

def get_node_map(dataset):
	with open(f'./data/{dataset}/train_node_map.json', 'r') as f:
		train_node_map = json.load(f)
	with open(f'./data/{dataset}/test_node_map.json', 'r') as f:
		test_node_map = json.load(f)
	return train_node_map, test_node_map

# def choose_poisonable_nodes(dataset, ratio, train_data):
# 	# if not new:
# 	# 	print('已选用固定投毒节点')
# 	# 	with open(f"./data/{dataset}/choosed_poisoned_nodes_uuid_{ratio}.json", "r") as f:
# 	# 		choosed_poisoned_nodes_uuid = json.load(f)
# 	# 	choosed_nodes_idx_all = {}
# 	# 	for key, value in choosed_poisoned_nodes_uuid.items():
# 	# 		key = int(key)
# 	# 		value = [train_node_map[key][i] for i in value]
# 	# 		choosed_nodes_idx_all[key] = value
# 	# 	return choosed_nodes_idx_all

# 	if isinstance(train_data, list):
# 		choosed_nodes_idx_all = {}

# 			print(f"训练集{i} 投毒节点数量: {len(choosed_nodes_uuid)}，投毒比例: {len(choosed_nodes_uuid)/process_num}")
   
# 		with open(f"./data/{dataset}/choosed_poisoned_nodes_uuid_{ratio}.json", "w") as f:
# 			json.dump(choosed_nodes_uuid_all, f)

# 	else:
# 		raise NotImplementedError
    
# 	return choosed_nodes_idx_all
