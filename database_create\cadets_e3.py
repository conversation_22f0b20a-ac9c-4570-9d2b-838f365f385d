import os
import re
import torch
from tqdm import tqdm
import psycopg2
from psycopg2 import extras as ex

def extract_subject_file_uuid(file_path, filelist):
    subject_uuid2path = {}
    file_uuid2path = {}

    for file in tqdm(filelist):
        with open(os.path.join(file_path, file), "r") as f:
            for line in (f):
                if "com.bbn.tc.schema.avro.cdm18.Subject" in line:
                    pattern = '{"com.bbn.tc.schema.avro.cdm18.Subject":{"uuid":"(.*?)"'
                    match_ans = re.findall(pattern, line)[0]
                    subject_uuid2path[match_ans] = None
                elif "com.bbn.tc.schema.avro.cdm18.FileObject" in line:
                    if "FILE_OBJECT_UNIX_SOCKET" in line:
                        continue
                    pattern = '{"com.bbn.tc.schema.avro.cdm18.FileObject":{"uuid":"(.*?)"'
                    match_ans = re.findall(pattern, line)[0]
                    file_uuid2path[match_ans] = None

    return subject_uuid2path, file_uuid2path

def store_netflow(file_path, cur, connect, index_id, filelist):
    # Parse data from logs
    netobjset = set()
    netobj2msg = {}
    for file in tqdm(filelist):
        with open(os.path.join(file_path, file), "r") as f:
            for line in f:
                if "NetFlowObject" in line:
                    try:
                        res = re.findall(
                            'NetFlowObject":{"uuid":"(.*?)"(.*?)"localAddress":"(.*?)","localPort":(.*?),"remoteAddress":"(.*?)","remotePort":(.*?),',
                            line)[0]

                        nodeid = res[0]
                        srcaddr = res[2]
                        srcport = res[3]
                        dstaddr = res[4]
                        dstport = res[5]

                        nodeproperty = [srcaddr, srcport, dstaddr, dstport]
                        netobj2msg[nodeid] = nodeproperty
                        netobjset.add(nodeid)
                    except:
                        pass
                if "FILE_OBJECT_UNIX_SOCKET" in line:
                    try:
                        res = re.findall('FileObject":{"uuid":"(.*?)"', line)
                        nodeid = res[0]
                        srcaddr = None
                        srcport = None
                        dstaddr = None
                        dstport = None

                        nodeproperty = [srcaddr, srcport, dstaddr, dstport]
                        netobj2msg[nodeid] = nodeproperty
                        netobjset.add(nodeid)
                    except:
                        print(line)

    # Store data into database
    datalist = []
    for i in netobj2msg.keys():
        datalist.append([i] + netobj2msg[i] + [index_id])  # uuid msg idx
        index_id += 1

    sql = '''insert into netflow_node_table
                         values %s
            '''
    ex.execute_values(cur, sql, datalist, page_size=10000)
    connect.commit()

    return index_id, netobj2msg

def store_subject(file_path, cur, connect, index_id, filelist, subject_uuid2path):
    # Parse data from logs
    scusess_count = 0
    fail_count = 0
    subject_obj2msg = {}
    for file in tqdm(filelist):
        with open(os.path.join(file_path, file), "r") as f:
            for line in (f):
                if "Event" in line:
                    subject_uuid = re.findall(
                        '"subject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"},(.*?)"exec":"(.*?)",', line)
                    try:
                        if subject_uuid[0][0] in subject_uuid2path:
                            subject_obj2msg[subject_uuid[0][0]] = [subject_uuid2path[subject_uuid[0][0]], subject_uuid[0][-1]] #{uuid:[path, cmd]}
                        scusess_count += 1
                    except:
                        try:
                            subject_obj2msg[subject_uuid[0][0]] = ["null","null"]
                        except:
                            pass
                        fail_count += 1
    # Store into database
    datalist = []
    for i in subject_obj2msg.keys():
        datalist.append([i] + subject_obj2msg[i] + [index_id])  # ([uuid, path, cmdLine, index_id])
        index_id += 1

    sql = '''insert into subject_node_table
                         values %s
            '''
    ex.execute_values(cur, sql, datalist, page_size=10000)
    connect.commit()
    print("fail_count:", fail_count)
    return index_id, subject_obj2msg

def store_file(file_path, cur, connect, index_id, filelist, file_uuid2path):
    file_obj2msg = {}
    fail_count = 0
    for file in tqdm(filelist):
        with open(os.path.join(file_path, file), "r") as f:
            for line in f:
                if "Event" in line:
                    try:
                        object_uuid = \
                            re.findall('"predicateObject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"},', line)[0]
                        if object_uuid in file_uuid2path:
                            if '"predicateObjectPath":null,' not in line and '<unknown>' not in line:
                                object_path = re.findall('"predicateObjectPath":{"string":"(.*?)"}', line)
                                if len(object_path) == 0:
                                    file_obj_name = None
                                else:
                                    file_obj_name = object_path[0]
                            else:
                                file_obj_name = None
                            file_obj2msg[object_uuid] = file_obj_name  #{uuid: path}
                    except:
                        fail_count += 1

    datalist = []
    for i in file_obj2msg.keys():
        datalist.append([i] + [file_obj2msg[i]] + [index_id])
        index_id += 1

    sql = '''insert into file_node_table
                         values %s
            '''
    ex.execute_values(cur, sql, datalist, page_size=10000)
    connect.commit()
    print("fail_count:", fail_count)
    return index_id, file_obj2msg

def create_node_list(cur):
    nodeid2msg = {}

    # netflow
    sql = """
        select * from netflow_node_table;
        """
    cur.execute(sql)
    records = cur.fetchall()
    for i in records:
        nodeid2msg[i[0]] = i[-1]

    # subject
    sql = """
    select * from subject_node_table;
    """
    cur.execute(sql)
    records = cur.fetchall()
    for i in records:
        nodeid2msg[i[0]] = i[-1]

    # file
    sql = """
    select * from file_node_table;
    """
    cur.execute(sql)
    records = cur.fetchall()
    for i in records:
        nodeid2msg[i[0]] = i[-1]

    return nodeid2msg #{uuid:index_id}

def write_event_in_DB(cur, connect, datalist):
    sql = '''insert into event_table
                         values %s
            '''
    ex.execute_values(cur,sql, datalist,page_size=10000)
    connect.commit()

def store_event(file_path, cur, connect, nodeid2msg, subject_uuid2msg, file_uuid2msg, net_uuid2msg, filelist):
    datalist = []
    for file in tqdm(filelist):
        with open(os.path.join(file_path, file), "r") as f:
            for line in f:
                if '{"datum":{"com.bbn.tc.schema.avro.cdm18.Event"' in line:
                    relation_type = re.findall('"type":"(.*?)"', line)[0]
                    subject_uuid = re.findall('"subject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"', line)
                    predicateObject_uuid = re.findall('"predicateObject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"', line)

                    if len(subject_uuid) > 0 and len(predicateObject_uuid) > 0:
                        if subject_uuid[0] in subject_uuid2msg and (predicateObject_uuid[0] in subject_uuid2msg or
                                                                        predicateObject_uuid[0] in file_uuid2msg or
                                                                        predicateObject_uuid[0] in net_uuid2msg):
                            event_uuid = re.findall('{"datum":{"com.bbn.tc.schema.avro.cdm18.Event":{"uuid":"(.*?)",', line)[0]
                            time_rec = re.findall('"timestampNanos":(.*?),', line)[0]
                            time_rec = int(time_rec)
                            subjectId = subject_uuid[0]
                            objectId = predicateObject_uuid[0]
                            if predicateObject_uuid[0] in file_uuid2msg:
                                obj_msg = file_uuid2msg[objectId]
                                obj_type = "File"
                            elif predicateObject_uuid[0] in net_uuid2msg:
                                obj_msg = net_uuid2msg[objectId]
                                obj_type = "NetFlow"
                            else:
                                obj_msg = subject_uuid2msg[objectId]
                                obj_type = "PROCESS"
                            datalist.append(
                                [subjectId, subject_uuid2msg[subjectId], nodeid2msg[subjectId], 'PROCESS', relation_type, 
                                 event_uuid, objectId, obj_msg, nodeid2msg[objectId], obj_type, time_rec])
    if filelist[0] == 'ta1-cadets-e3-official.json.1':
        sql = '''insert into train_event_table
                            values %s
                '''
        ex.execute_values(cur, sql, datalist, page_size=50000)
        connect.commit()
    else:
        sql = '''insert into test_event_table
                            values %s
                '''
        ex.execute_values(cur, sql, datalist, page_size=50000)
        connect.commit()

def init_database_connection():
    connect = psycopg2.connect(database = 'cadets_threatrace_flash',
                                host = 'localhost',
                                user = 'postgres',
                                password = '123456',
                                port = 5432
                                )
    cur = connect.cursor()
    return cur, connect

def main():
    raw_dir = r"F:\DARPA_TC_E3\data\cadets"
    filelist = []
    for file in os.listdir(raw_dir):
        if 'json' in file:
            filelist.append(file)

    cur, connect = init_database_connection()

    subject_uuid2path, file_uuid2path = extract_subject_file_uuid(file_path=raw_dir,filelist=filelist)  # {uuid:None}

    index_id = 0

    print("Processing netflow data")  # 2143601
    index_id, net_uuid2msg = store_netflow(file_path=raw_dir, cur=cur, connect=connect, index_id=index_id, filelist=filelist)

    print("Processing subject data")  # 224146
    index_id, subject_uuid2msg = store_subject(file_path=raw_dir, cur=cur, connect=connect, index_id=index_id, filelist=filelist, subject_uuid2path=subject_uuid2path)

    print("Processing file data")  # 316878
    index_id, file_uuid2msg = store_file(file_path=raw_dir, cur=cur, connect=connect, index_id=index_id, filelist=filelist, file_uuid2path=file_uuid2path)

    print("Extracting the node list")
    nodeid2msg = create_node_list(cur=cur)   #{uuid:index_id}

    print("Processing the events")  # 36484667
    
    store_event(
        file_path=raw_dir,
        cur=cur,
        connect=connect,
        nodeid2msg=nodeid2msg,
        subject_uuid2msg=subject_uuid2msg,
        file_uuid2msg=file_uuid2msg,
        net_uuid2msg=net_uuid2msg,
        filelist=['ta1-cadets-e3-official.json.1']
    )
    store_event(
        file_path=raw_dir,
        cur=cur,
        connect=connect,
        nodeid2msg=nodeid2msg,
        subject_uuid2msg=subject_uuid2msg,
        file_uuid2msg=file_uuid2msg,
        net_uuid2msg=net_uuid2msg,
        filelist=['ta1-cadets-e3-official-2.json']
    )

if __name__ == "__main__":
    main()