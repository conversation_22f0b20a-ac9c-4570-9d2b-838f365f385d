from py2neo import Graph, Node, Relationship
import pandas as pd
import tqdm

# 创建索引
def create_indexes():
    graph.run("CREATE INDEX FOR (n:Node) ON (n.name);")
    graph.run("CREATE INDEX FOR ()-[e:Event]-() ON (e.type);")
    graph.run("CREATE INDEX FOR ()-[e:Event]-() ON (e.src_uuid);")
    graph.run("CREATE INDEX FOR ()-[e:Event]-() ON (e.dst_uuid);")
    print("索引创建完成！")

# 读取CSV文件
def read_csv(file_path):
    return pd.read_csv(file_path)

# 获取或创建节点
def get_or_create_node(node_id, node_name, node_index_id, node_type):
    # 生成缓存键
    cache_key = f"{node_type}_{node_id}"
    
    # 如果节点已在缓存中，直接返回
    if cache_key in node_cache:
        return node_cache[cache_key]
    
    # 如果节点不在缓存中，则直接创建新节点并加入缓存
    new_node = Node(node_type, uuid=node_id, name=node_name, index_id=node_index_id)
    graph.create(new_node)
    node_cache[cache_key] = new_node
    return new_node

# 批量插入节点和关系
def build_graph_from_csv(file_path):
    # 读取CSV文件
    data = read_csv(file_path)
    
    # 开启事务
    tx = graph.begin()
    
    # 遍历每一行数据
    for index, row in tqdm.tqdm(data.iterrows(), total=len(data)):
        # 获取源节点信息
        src_id = row["src_node"]
        src_name = row["src_name"]
        src_index_id = row["src_index_id"]
        src_type = row["src_type"]
        
        # 获取目标节点信息
        dst_id = row["dst_node"]
        dst_name = row["dst_name"]
        dst_index_id = row["dst_index_id"]
        dst_type = row["dst_type"]
        
        # 获取关系信息
        relationship_type = row["operation"]
        event_id = row["event_id"]
        timestamp = row["timestamp_rec"]
        
        # 获取或创建源节点和目标节点
        src_node = get_or_create_node(src_id, src_name, src_index_id, src_type)
        dst_node = get_or_create_node(dst_id, dst_name, dst_index_id, dst_type)
        
        # 创建关系
        relationship = Relationship(src_node, relationship_type, dst_node,
                                    type=relationship_type, index_id=event_id,
                                    timestamp=timestamp, src_uuid=src_id, dst_uuid=dst_id)
        tx.create(relationship)
        # tx.merge(relationship)
        if index/len(data) <0.1:
            if index % 300 == 0 and index != 0:
                graph.commit(tx)  # 提交事务
                tx = graph.begin()  # 开始一个新事务
        else:
            if index % 1000 == 0 and index != 0:
                graph.commit(tx)  # 提交事务
                tx = graph.begin()  # 开始一个新事务           
    
    # 提交事务
    graph.commit(tx)
    print("图数据库构建完成！")

# 示例：调用函数构建图数据库
if __name__ == "__main__":
    # 创建索引
    # create_indexes()
    # 连接Neo4j数据库
    global graph, node_cache
    
    file_path_list = ["event_table_6r_0.csv", "event_table_6r_1.csv","event_table_6r_2.csv","event_table_6r_3.csv","event_table_6r_8.csv"]
    i = 0
    for name in ['theiamagic6r0', 'theiamagic6r1','theiamagic6r2','theiamagic6r3','theiamagic6r8']:
        # 缓存节点，避免重复查询
        node_cache = {}
        
        print(f'========0正在{i}========')
        graph = Graph("bolt://localhost:7687", auth=("neo4j", "12345678"),name=name)
        print("连接成功！")
        # 构建图数据库
        file_path = file_path_list[i]
        build_graph_from_csv(file_path)
        i += 1