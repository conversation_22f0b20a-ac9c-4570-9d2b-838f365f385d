from py2neo import Graph, Node, Relationship
import dgl
import tqdm
import pickle as pkl
import json

with open(f'./data/theia/node_type_dict.json', 'r') as f:
    node_type_dict = json.load(f)
with open(f'./data/theia/edge_type_dict.json', 'r') as f:
    edge_type_dict = json.load(f)
node_type_dict = {v:k for k,v in node_type_dict.items()}
edge_type_dict = {v:k for k,v in edge_type_dict.items()}

def dgl_to_neo4j_filtered(dgl_graph: dgl.DGLGraph, graph_name: str):
    """
    将过滤后的DGL图导入Neo4j数据库
    
    参数:
        dgl_graph: DGL图对象
        graph_name: Neo4j数据库名称
    """
    # 连接Neo4j
    neo4j_graph = Graph("bolt://localhost:7687", auth=("neo4j", "12345678"), name=graph_name)
    node_cache = {}  # 节点缓存
    valid_node_types = {0, 1, 3}  # 只处理这些类型的节点
    
    # 获取节点和边的属性
    node_data = dgl_graph.ndata
    edge_data = dgl_graph.edata
    
    # 开始事务
    tx = neo4j_graph.begin()
    
    # 1. 插入符合条件的节点
    print("正在插入过滤后的节点...")
    nodes = dgl_graph.nodes()
    valid_nodes = []
    
    for node_id in tqdm.tqdm(nodes, desc="Filtering Nodes"):
        node_type = node_data['type'][node_id].item()
        if node_type in valid_node_types:
            valid_nodes.append(node_id.item())
    valid_nodes_num = 0
    for node_id in tqdm.tqdm(valid_nodes, desc="Inserting Nodes"):
        node_type = node_data['type'][node_id].item()
        node_type = node_type_dict[node_type]
        # 使用type作为label，index_id作为属性
        node = Node(node_type, index_id=node_id)
        node_cache[node_id] = node
        tx.create(node)
        valid_nodes_num += 1
        if valid_nodes_num % 1000 == 0 and valid_nodes_num != 0:
            neo4j_graph.commit(tx)
            tx = neo4j_graph.begin()
    
    # 2. 插入符合条件的边
    print("正在插入过滤后的边...")
    src_nodes, dst_nodes = dgl_graph.edges()
    valid_edges = 0
    
    for idx in tqdm.tqdm(range(len(src_nodes)), desc="Filtering Edges"):
        src_id = src_nodes[idx].item()
        dst_id = dst_nodes[idx].item()
        
        # 检查源节点和目标节点是否都在有效节点中
        if src_id in node_cache and dst_id in node_cache:
            valid_edges += 1
            edge_type = edge_type_dict[edge_data['type'][idx].item()]
            
            # 创建关系，使用type作为关系类型
            relationship = Relationship(
                node_cache[src_id], 
                edge_type, 
                node_cache[dst_id],
                index_id=idx
            )
            tx.create(relationship)
            
            # 批量提交(每1000条边提交一次)
            if valid_edges % 1000 == 0 and valid_edges != 0:
                neo4j_graph.commit(tx)
                tx = neo4j_graph.begin()
    
    # 最终提交
    neo4j_graph.commit(tx)
    print(f"图 {graph_name} 导入完成！共 {len(valid_nodes)} 个节点, {valid_edges} 条边")

# 示例用法
if __name__ == "__main__":
    t = 'train'
    n = 1
    with open('./data/theia' + '/{}{}_poisoned.pkl'.format(t, n), 'rb') as f:
        g = pkl.load(f)
    
    # 导入到Neo4j
    dgl_to_neo4j_filtered(g, "theiamagic6r1p")