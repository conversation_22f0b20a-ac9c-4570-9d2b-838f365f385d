#!/usr/bin/env python3
"""
深入分析恶意节点与训练节点相似性的脚本
重点分析为什么恶意NetFlowObject节点与训练集节点如此相似
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
import json

from darpatc import *
from utils.config import build_args
from model.autoencoder import build_model
from utils.loaddata import load_entity_level_dataset, load_metadata
from attack_utils import *

class DeepSimilarityAnalyzer:
    def __init__(self, cfg):
        self.cfg = cfg
        self.device = cfg.device
        
        # 加载数据和模型
        self.setup_data_and_model()
        
    def setup_data_and_model(self):
        """设置数据和模型"""
        print("=====设置数据和模型=====")
        
        # 加载数据
        self.metadata = load_metadata(self.cfg.dataset)
        self.train_data_all = []
        self.test_data_all = []
        
        for i in range(self.metadata['n_train']):
            g = load_entity_level_dataset(self.cfg.dataset, 'train', i)
            self.train_data_all.append(g)
        for i in range(self.metadata['n_test']):
            g = load_entity_level_dataset(self.cfg.dataset, 'test', i)
            self.test_data_all.append(g)
        
        # 设置配置
        self.cfg.train_node_map, self.cfg.test_node_map = get_node_map(self.cfg.dataset)
        self.cfg.node_type_dict, self.cfg.edge_type_dict = get_map(self.cfg.dataset)
        self.cfg.n_dim = self.metadata['node_feature_dim']
        self.cfg.e_dim = self.metadata['edge_feature_dim']
        
        # 获取恶意节点信息
        self.malicious_node, self.mal_file_msg, self.mal_socket_msg = get_mal_node_msg(self.cfg, self.test_data_all[0])
        
        # 加载模型
        model_cfg = build_args()
        model_cfg.num_hidden = 64
        model_cfg.num_layers = 3
        model_cfg.n_dim = self.metadata['node_feature_dim']
        model_cfg.e_dim = self.metadata['edge_feature_dim']
        
        self.detector = build_model(model_cfg)
        self.detector = self.detector.to(self.device)
        
        # 加载投毒模型
        if os.path.exists('./poison_model/detector66.pt'):
            self.detector.load_state_dict(torch.load('./poison_model/detector66.pt'))
            print("✓ 加载投毒模型")
        else:
            self.detector.load_state_dict(torch.load('./checkpoints/checkpoint-theia.pt'))
            print("✓ 加载原始模型")
        
        self.detector.eval()
        
    def analyze_node_features(self):
        """分析节点特征相似性"""
        print("\n=====分析节点特征相似性=====")
        
        test_g = self.test_data_all[0]
        train_g = self.train_data_all[0]  # 分析第一个训练图
        
        # 获取NetFlowObject节点
        mal_netflow_nodes = self.malicious_node['NetFlowObject'][:100]  # 取前100个进行分析
        train_netflow_mask = (train_g.ndata['type'] == self.cfg.node_type_dict['NetFlowObject'])
        train_netflow_indices = train_netflow_mask.nonzero().squeeze()
        
        if train_netflow_indices.dim() == 0:
            train_netflow_indices = train_netflow_indices.unsqueeze(0)
        
        # 获取原始特征
        mal_features = test_g.ndata['attr'][mal_netflow_nodes]
        train_features = train_g.ndata['attr'][train_netflow_indices]
        
        print(f"恶意NetFlowObject特征形状: {mal_features.shape}")
        print(f"训练NetFlowObject特征形状: {train_features.shape}")
        
        # 分析特征分布
        print(f"\n特征统计:")
        print(f"恶意节点特征均值: {mal_features.mean(dim=0)}")
        print(f"训练节点特征均值: {train_features.mean(dim=0)}")
        print(f"恶意节点特征标准差: {mal_features.std(dim=0)}")
        print(f"训练节点特征标准差: {train_features.std(dim=0)}")
        
        # 计算特征相似性
        mal_features_norm = torch.nn.functional.normalize(mal_features, p=2, dim=1)
        train_features_norm = torch.nn.functional.normalize(train_features, p=2, dim=1)
        
        # 计算余弦相似度
        cosine_sim = torch.mm(mal_features_norm, train_features_norm.t())
        max_cosine_sim = cosine_sim.max(dim=1)[0]
        
        print(f"\n余弦相似度统计:")
        print(f"最大余弦相似度范围: {max_cosine_sim.min().item():.4f} - {max_cosine_sim.max().item():.4f}")
        print(f"平均最大余弦相似度: {max_cosine_sim.mean().item():.4f}")
        
        # 检查完全相同的特征
        identical_features = 0
        for mal_feat in mal_features:
            for train_feat in train_features:
                if torch.allclose(mal_feat, train_feat, atol=1e-6):
                    identical_features += 1
                    break
        
        print(f"完全相同特征的恶意节点数: {identical_features}/{len(mal_features)}")
        
        return mal_features, train_features, cosine_sim
    
    def analyze_neighborhood_patterns(self):
        """分析邻域模式相似性"""
        print("\n=====分析邻域模式相似性=====")
        
        test_g = self.test_data_all[0]
        train_g = self.train_data_all[0]
        
        # 选择一些相似的节点对进行分析
        mal_netflow_nodes = self.malicious_node['NetFlowObject'][:50]  # 取前50个
        train_netflow_mask = (train_g.ndata['type'] == self.cfg.node_type_dict['NetFlowObject'])
        train_netflow_indices = train_netflow_mask.nonzero().squeeze()
        
        if train_netflow_indices.dim() == 0:
            train_netflow_indices = train_netflow_indices.unsqueeze(0)
        
        # 计算嵌入相似性找到最相似的节点对
        with torch.no_grad():
            test_embeddings = self.detector.embed(test_g)
            train_embeddings = self.detector.embed(train_g)
        
        mal_embeddings = test_embeddings[mal_netflow_nodes]
        train_netflow_embeddings = train_embeddings[train_netflow_indices]
        
        distances = torch.cdist(mal_embeddings, train_netflow_embeddings, p=2)
        min_distances, min_indices = distances.min(dim=1)
        
        # 分析最相似的几对节点
        print(f"分析最相似的节点对:")
        
        for i in range(min(5, len(mal_netflow_nodes))):
            mal_node = mal_netflow_nodes[i]
            closest_train_idx = train_netflow_indices[min_indices[i]]
            distance = min_distances[i].item()
            
            print(f"\n节点对 {i+1}:")
            print(f"  恶意节点: {mal_node}, 训练节点: {closest_train_idx.item()}")
            print(f"  嵌入距离: {distance:.4f}")
            
            # 分析邻域
            self.analyze_single_node_neighborhood(test_g, mal_node, "恶意")
            self.analyze_single_node_neighborhood(train_g, closest_train_idx.item(), "训练")
    
    def analyze_single_node_neighborhood(self, graph, node_idx, node_type):
        """分析单个节点的邻域"""
        predecessors = graph.predecessors(node_idx).tolist()
        successors = graph.successors(node_idx).tolist()
        
        print(f"    {node_type}节点 {node_idx} 邻域:")
        print(f"      前驱节点数: {len(predecessors)}")
        print(f"      后继节点数: {len(successors)}")
        
        # 分析前驱节点类型
        if predecessors:
            pred_types = graph.ndata['type'][predecessors]
            type_counts = Counter()
            for type_id in pred_types:
                for name, id in self.cfg.node_type_dict.items():
                    if id == type_id.item():
                        type_counts[name] += 1
                        break
            print(f"      前驱节点类型: {dict(type_counts)}")
        
        # 分析入边类型
        in_edges = graph.in_edges(node_idx, form='eid')
        if in_edges.numel() > 0:
            in_edge_types = graph.edata['type'][in_edges]
            edge_type_counts = Counter()
            for type_id in in_edge_types:
                for name, id in self.cfg.edge_type_dict.items():
                    if id == type_id.item():
                        edge_type_counts[name] += 1
                        break
            print(f"      入边类型: {dict(edge_type_counts)}")
    
    def compare_with_clean_model(self):
        """与干净模型对比"""
        print("\n=====与干净模型对比=====")
        
        # 加载干净模型
        clean_model_cfg = build_args()
        clean_model_cfg.num_hidden = 64
        clean_model_cfg.num_layers = 3
        clean_model_cfg.n_dim = self.metadata['node_feature_dim']
        clean_model_cfg.e_dim = self.metadata['edge_feature_dim']

        clean_detector = build_model(clean_model_cfg)
        clean_detector.load_state_dict(torch.load('./checkpoints/checkpoint-theia.pt'))
        clean_detector = clean_detector.to(self.device)
        clean_detector.eval()
        
        test_g = self.test_data_all[0]
        train_g = self.train_data_all[0]
        
        mal_netflow_nodes = self.malicious_node['NetFlowObject'][:1000]  # 取前1000个
        train_netflow_mask = (train_g.ndata['type'] == self.cfg.node_type_dict['NetFlowObject'])
        train_netflow_indices = train_netflow_mask.nonzero().squeeze()
        
        if train_netflow_indices.dim() == 0:
            train_netflow_indices = train_netflow_indices.unsqueeze(0)
        
        # 计算干净模型的嵌入
        with torch.no_grad():
            clean_test_embeddings = clean_detector.embed(test_g)
            clean_train_embeddings = clean_detector.embed(train_g)
            
            poisoned_test_embeddings = self.detector.embed(test_g)
            poisoned_train_embeddings = self.detector.embed(train_g)
        
        # 分析恶意节点在两个模型中的距离变化
        clean_mal_embeddings = clean_test_embeddings[mal_netflow_nodes]
        clean_train_netflow_embeddings = clean_train_embeddings[train_netflow_indices]
        
        poisoned_mal_embeddings = poisoned_test_embeddings[mal_netflow_nodes]
        poisoned_train_netflow_embeddings = poisoned_train_embeddings[train_netflow_indices]
        
        # 计算距离
        clean_distances = torch.cdist(clean_mal_embeddings, clean_train_netflow_embeddings, p=2)
        poisoned_distances = torch.cdist(poisoned_mal_embeddings, poisoned_train_netflow_embeddings, p=2)
        
        clean_min_distances = clean_distances.min(dim=1)[0]
        poisoned_min_distances = poisoned_distances.min(dim=1)[0]
        
        print(f"干净模型距离统计:")
        print(f"  平均最小距离: {clean_min_distances.mean().item():.4f}")
        print(f"  距离范围: {clean_min_distances.min().item():.4f} - {clean_min_distances.max().item():.4f}")
        print(f"  距离<0.1的节点数: {(clean_min_distances < 0.1).sum().item()}")
        
        print(f"投毒模型距离统计:")
        print(f"  平均最小距离: {poisoned_min_distances.mean().item():.4f}")
        print(f"  距离范围: {poisoned_min_distances.min().item():.4f} - {poisoned_min_distances.max().item():.4f}")
        print(f"  距离<0.1的节点数: {(poisoned_min_distances < 0.1).sum().item()}")
        
        # 分析距离变化
        distance_change = poisoned_min_distances - clean_min_distances
        print(f"距离变化统计:")
        print(f"  平均变化: {distance_change.mean().item():.4f}")
        print(f"  变化范围: {distance_change.min().item():.4f} - {distance_change.max().item():.4f}")
        
        # 检查是否投毒使恶意节点更接近训练节点
        closer_nodes = (distance_change < -0.01).sum().item()
        farther_nodes = (distance_change > 0.01).sum().item()
        
        print(f"投毒后变得更接近训练节点的恶意节点数: {closer_nodes}")
        print(f"投毒后变得更远离训练节点的恶意节点数: {farther_nodes}")
        
        return clean_min_distances, poisoned_min_distances, distance_change
    
    def visualize_distance_comparison(self, clean_distances, poisoned_distances, distance_change):
        """可视化距离对比"""
        print("\n=====可视化距离对比=====")
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 距离分布对比
        axes[0, 0].hist(clean_distances.cpu().numpy(), bins=50, alpha=0.7, label='Clean Model', color='blue')
        axes[0, 0].hist(poisoned_distances.cpu().numpy(), bins=50, alpha=0.7, label='Poisoned Model', color='red')
        axes[0, 0].set_xlabel('Minimum Distance')
        axes[0, 0].set_ylabel('Count')
        axes[0, 0].set_title('Distance Distribution Comparison')
        axes[0, 0].legend()
        
        # 距离变化分布
        axes[0, 1].hist(distance_change.cpu().numpy(), bins=50, alpha=0.7, color='green')
        axes[0, 1].set_xlabel('Distance Change (Poisoned - Clean)')
        axes[0, 1].set_ylabel('Count')
        axes[0, 1].set_title('Distance Change Distribution')
        axes[0, 1].axvline(0, color='black', linestyle='--', alpha=0.5)
        
        # 散点图对比
        axes[1, 0].scatter(clean_distances.cpu().numpy(), poisoned_distances.cpu().numpy(), alpha=0.5, s=1)
        axes[1, 0].plot([0, 2], [0, 2], 'r--', alpha=0.5)  # 对角线
        axes[1, 0].set_xlabel('Clean Model Distance')
        axes[1, 0].set_ylabel('Poisoned Model Distance')
        axes[1, 0].set_title('Distance Correlation')
        
        # 累积分布函数
        clean_sorted = torch.sort(clean_distances)[0].cpu().numpy()
        poisoned_sorted = torch.sort(poisoned_distances)[0].cpu().numpy()
        
        x_clean = np.linspace(0, 1, len(clean_sorted))
        x_poisoned = np.linspace(0, 1, len(poisoned_sorted))
        
        axes[1, 1].plot(clean_sorted, x_clean, label='Clean Model', color='blue')
        axes[1, 1].plot(poisoned_sorted, x_poisoned, label='Poisoned Model', color='red')
        axes[1, 1].set_xlabel('Distance')
        axes[1, 1].set_ylabel('Cumulative Probability')
        axes[1, 1].set_title('Cumulative Distribution')
        axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig('distance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    from poison_main import Config
    cfg = Config()
    
    analyzer = DeepSimilarityAnalyzer(cfg)
    
    # 1. 分析节点特征相似性
    mal_features, train_features, cosine_sim = analyzer.analyze_node_features()
    
    # 2. 分析邻域模式
    analyzer.analyze_neighborhood_patterns()
    
    # 3. 与干净模型对比
    clean_distances, poisoned_distances, distance_change = analyzer.compare_with_clean_model()
    
    # 4. 可视化对比
    analyzer.visualize_distance_comparison(clean_distances, poisoned_distances, distance_change)
    
    print("\n=====深入分析完成=====")
    print("关键发现:")
    print("1. 检查恶意节点与训练节点的原始特征相似性")
    print("2. 分析邻域结构模式")
    print("3. 对比投毒前后模型的行为差异")
    print("4. 可视化距离分布变化")

if __name__ == "__main__":
    main()
