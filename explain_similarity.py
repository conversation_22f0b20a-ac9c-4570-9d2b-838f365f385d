#!/usr/bin/env python3
"""
使用解释性方法分析节点相似性的脚本
利用explain.py中的GNNExplainer来解释为什么某些节点被认为相似
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import json

from darpatc import *
from utils.config import build_args
from model.autoencoder import build_model
from utils.loaddata import load_entity_level_dataset, load_metadata
from attack_utils import *
from explain import *

class SimilarityExplainer:
    def __init__(self, cfg):
        self.cfg = cfg
        self.device = cfg.device
        
        # 设置数据和模型
        self.setup_data_and_model()
        
        # 创建解释器
        self.setup_explainer()
        
    def setup_data_and_model(self):
        """设置数据和模型"""
        print("=====设置数据和模型=====")
        
        # 加载数据
        self.metadata = load_metadata(self.cfg.dataset)
        self.train_data_all = []
        self.test_data_all = []
        
        for i in range(self.metadata['n_train']):
            g = load_entity_level_dataset(self.cfg.dataset, 'train', i)
            self.train_data_all.append(g)
        for i in range(self.metadata['n_test']):
            g = load_entity_level_dataset(self.cfg.dataset, 'test', i)
            self.test_data_all.append(g)
        
        # 设置配置
        self.cfg.train_node_map, self.cfg.test_node_map = get_node_map(self.cfg.dataset)
        self.cfg.node_type_dict, self.cfg.edge_type_dict = get_map(self.cfg.dataset)
        self.cfg.n_dim = self.metadata['node_feature_dim']
        self.cfg.e_dim = self.metadata['edge_feature_dim']
        
        # 获取恶意节点信息
        self.malicious_node, self.mal_file_msg, self.mal_socket_msg = get_mal_node_msg(self.cfg, self.test_data_all[0])
        
        # 加载模型
        model_cfg = build_args()
        model_cfg.num_hidden = 64
        model_cfg.num_layers = 3
        model_cfg.n_dim = self.metadata['node_feature_dim']
        model_cfg.e_dim = self.metadata['edge_feature_dim']
        
        self.detector = build_model(model_cfg)
        self.detector = self.detector.to(self.device)
        
        # 加载投毒模型
        if os.path.exists('./poison_model/detector66.pt'):
            self.detector.load_state_dict(torch.load('./poison_model/detector66.pt'))
            print("加载投毒模型")
        else:
            self.detector.load_state_dict(torch.load('./checkpoints/checkpoint-theia.pt'))
            print("加载原始模型")
        
        self.detector.eval()
        
    def setup_explainer(self):
        """设置解释器"""
        print("=====设置解释器=====")
        
        # 确保模型参数需要梯度
        for param in self.detector.parameters():
            param.requires_grad = True
        
        # 创建包装器
        self.wrapped_model = GATExplainerWrapper(self.detector)
        self.wrapped_model.eval()
        
        # 创建解释器
        self.explainer = GNNExplainer(
            model=self.wrapped_model,
            num_hops=3,
            lr=0.01,
            alpha1=0.01, alpha2=1.5, beta1=0.5, beta2=0.1,
            num_epochs=200,
        )
        
    def find_similar_nodes(self):
        """找到相似的节点对"""
        print("\n=====寻找相似节点对=====")
        
        # 计算嵌入
        test_g = self.test_data_all[0]
        train_embeddings = []
        train_node_info = []
        
        for g_idx, g in enumerate(self.train_data_all):
            with torch.no_grad():
                emb = self.detector.embed(g)
                train_embeddings.append(emb)
                
                # 记录NetFlowObject节点
                netflow_mask = (g.ndata['type'] == self.cfg.node_type_dict['NetFlowObject'])
                netflow_indices = netflow_mask.nonzero().squeeze()
                if netflow_indices.numel() > 0:
                    if netflow_indices.dim() == 0:
                        netflow_indices = netflow_indices.unsqueeze(0)
                    for idx in netflow_indices:
                        train_node_info.append((g_idx, idx.item()))
        
        train_embeddings = torch.cat(train_embeddings, dim=0)
        
        # 获取测试集恶意NetFlowObject节点
        with torch.no_grad():
            test_embeddings = self.detector.embed(test_g)
        
        mal_netflow_nodes = self.malicious_node['NetFlowObject']
        if len(mal_netflow_nodes) == 0:
            print("没有找到恶意NetFlowObject节点")
            return None
        
        mal_embeddings = test_embeddings[mal_netflow_nodes]
        
        # 获取训练集NetFlowObject节点嵌入
        train_netflow_embeddings = []
        train_netflow_info = []
        
        for g_idx, g in enumerate(self.train_data_all):
            with torch.no_grad():
                emb = self.detector.embed(g)
                netflow_mask = (g.ndata['type'] == self.cfg.node_type_dict['NetFlowObject'])
                netflow_indices = netflow_mask.nonzero().squeeze()
                if netflow_indices.numel() > 0:
                    if netflow_indices.dim() == 0:
                        netflow_indices = netflow_indices.unsqueeze(0)
                    train_netflow_embeddings.append(emb[netflow_indices])
                    train_netflow_info.extend([(g_idx, idx.item()) for idx in netflow_indices])
        
        if not train_netflow_embeddings:
            print("没有找到训练集NetFlowObject节点")
            return None
            
        train_netflow_embeddings = torch.cat(train_netflow_embeddings, dim=0)
        
        # 计算距离
        distances = torch.cdist(mal_embeddings, train_netflow_embeddings, p=2)
        
        # 找到最相似的节点对
        min_distance_idx = distances.argmin()
        mal_idx = min_distance_idx // distances.shape[1]
        train_idx = min_distance_idx % distances.shape[1]
        
        similar_mal_node = mal_netflow_nodes[mal_idx.item()]
        similar_train_info = train_netflow_info[train_idx.item()]
        min_distance = distances[mal_idx, train_idx].item()
        
        print(f"最相似的节点对:")
        print(f"  恶意节点: {similar_mal_node}")
        print(f"  训练节点: 图{similar_train_info[0]}节点{similar_train_info[1]}")
        print(f"  距离: {min_distance:.4f}")
        
        return {
            'mal_node': similar_mal_node,
            'train_node_info': similar_train_info,
            'distance': min_distance,
            'test_graph': test_g,
            'train_graph': self.train_data_all[similar_train_info[0]]
        }
    
    def explain_malicious_node(self, node_info):
        """解释恶意节点"""
        print(f"\n=====解释恶意节点 {node_info['mal_node']}=====")
        
        test_g = node_info['test_graph'].to(self.device)
        mal_node = node_info['mal_node']
        
        try:
            # 解释恶意节点
            new_center, subgraph, node_feat_mask, edge_mask = self.explainer.explain_node(
                mal_node, test_g, test_g.ndata['attr']
            )
            
            print(f"解释结果:")
            print(f"  中心节点: {new_center}")
            print(f"  子图节点数: {subgraph.num_nodes()}")
            print(f"  子图边数: {subgraph.num_edges()}")
            print(f"  节点特征掩码形状: {node_feat_mask.shape}")
            print(f"  边掩码形状: {edge_mask.shape}")
            
            # 可视化解释结果
            save_path = f"explanation_malicious_node_{mal_node}.png"
            visualize_explanation(
                subgraph, edge_mask, node_feat_mask, new_center, 
                edge_threshold=0.1, save_path=save_path
            )
            
            return {
                'subgraph': subgraph,
                'node_feat_mask': node_feat_mask,
                'edge_mask': edge_mask,
                'center': new_center
            }
            
        except Exception as e:
            print(f"解释恶意节点时出错: {e}")
            return None
    
    def explain_similar_train_node(self, node_info):
        """解释相似的训练节点"""
        train_info = node_info['train_node_info']
        train_g = node_info['train_graph'].to(self.device)
        train_node = train_info[1]
        
        print(f"\n=====解释训练节点 图{train_info[0]}节点{train_node}=====")
        
        try:
            # 解释训练节点
            new_center, subgraph, node_feat_mask, edge_mask = self.explainer.explain_node(
                train_node, train_g, train_g.ndata['attr']
            )
            
            print(f"解释结果:")
            print(f"  中心节点: {new_center}")
            print(f"  子图节点数: {subgraph.num_nodes()}")
            print(f"  子图边数: {subgraph.num_edges()}")
            print(f"  节点特征掩码形状: {node_feat_mask.shape}")
            print(f"  边掩码形状: {edge_mask.shape}")
            
            # 可视化解释结果
            save_path = f"explanation_train_node_g{train_info[0]}_n{train_node}.png"
            visualize_explanation(
                subgraph, edge_mask, node_feat_mask, new_center, 
                edge_threshold=0.1, save_path=save_path
            )
            
            return {
                'subgraph': subgraph,
                'node_feat_mask': node_feat_mask,
                'edge_mask': edge_mask,
                'center': new_center
            }
            
        except Exception as e:
            print(f"解释训练节点时出错: {e}")
            return None
    
    def compare_explanations(self, mal_explanation, train_explanation):
        """比较两个解释结果"""
        print("\n=====比较解释结果=====")
        
        if mal_explanation is None or train_explanation is None:
            print("无法比较，某个解释结果为空")
            return
        
        # 比较子图大小
        mal_subgraph = mal_explanation['subgraph']
        train_subgraph = train_explanation['subgraph']
        
        print(f"子图大小比较:")
        print(f"  恶意节点子图: {mal_subgraph.num_nodes()} 节点, {mal_subgraph.num_edges()} 边")
        print(f"  训练节点子图: {train_subgraph.num_nodes()} 节点, {train_subgraph.num_edges()} 边")
        
        # 比较边重要性分布
        mal_edge_mask = mal_explanation['edge_mask']
        train_edge_mask = train_explanation['edge_mask']
        
        print(f"边重要性统计:")
        print(f"  恶意节点 - 平均: {mal_edge_mask.mean():.4f}, 最大: {mal_edge_mask.max():.4f}")
        print(f"  训练节点 - 平均: {train_edge_mask.mean():.4f}, 最大: {train_edge_mask.max():.4f}")
        
        # 分析高重要性边的数量
        threshold = 0.5
        mal_important_edges = (mal_edge_mask > threshold).sum().item()
        train_important_edges = (train_edge_mask > threshold).sum().item()
        
        print(f"高重要性边数量 (阈值 {threshold}):")
        print(f"  恶意节点: {mal_important_edges}")
        print(f"  训练节点: {train_important_edges}")
    
    def analyze_node_neighborhoods(self, node_info):
        """分析节点邻域"""
        print("\n=====分析节点邻域=====")
        
        test_g = node_info['test_graph']
        train_g = node_info['train_graph']
        mal_node = node_info['mal_node']
        train_node = node_info['train_node_info'][1]
        
        # 分析恶意节点邻域
        print(f"恶意节点 {mal_node} 的邻域:")
        mal_predecessors = test_g.predecessors(mal_node).tolist()
        mal_successors = test_g.successors(mal_node).tolist()
        
        print(f"  前驱节点: {len(mal_predecessors)} 个")
        print(f"  后继节点: {len(mal_successors)} 个")
        
        # 分析邻居节点类型
        if mal_predecessors:
            pred_types = test_g.ndata['type'][mal_predecessors]
            type_counts = {}
            for type_id in pred_types:
                for name, id in self.cfg.node_type_dict.items():
                    if id == type_id.item():
                        type_counts[name] = type_counts.get(name, 0) + 1
                        break
            print(f"  前驱节点类型: {type_counts}")
        
        # 分析训练节点邻域
        print(f"训练节点 {train_node} 的邻域:")
        train_predecessors = train_g.predecessors(train_node).tolist()
        train_successors = train_g.successors(train_node).tolist()
        
        print(f"  前驱节点: {len(train_predecessors)} 个")
        print(f"  后继节点: {len(train_successors)} 个")
        
        if train_predecessors:
            pred_types = train_g.ndata['type'][train_predecessors]
            type_counts = {}
            for type_id in pred_types:
                for name, id in self.cfg.node_type_dict.items():
                    if id == type_id.item():
                        type_counts[name] = type_counts.get(name, 0) + 1
                        break
            print(f"  前驱节点类型: {type_counts}")

def main():
    from poison_main import Config
    cfg = Config()
    
    explainer = SimilarityExplainer(cfg)
    
    # 1. 找到相似的节点对
    node_info = explainer.find_similar_nodes()
    
    if node_info is None:
        print("未找到相似节点对，退出")
        return
    
    # 2. 分析节点邻域
    explainer.analyze_node_neighborhoods(node_info)
    
    # 3. 解释恶意节点
    mal_explanation = explainer.explain_malicious_node(node_info)
    
    # 4. 解释相似的训练节点
    train_explanation = explainer.explain_similar_train_node(node_info)
    
    # 5. 比较解释结果
    explainer.compare_explanations(mal_explanation, train_explanation)
    
    print("\n=====解释分析完成=====")
    print("请查看生成的解释图片以了解节点相似性的原因")

if __name__ == "__main__":
    main()
