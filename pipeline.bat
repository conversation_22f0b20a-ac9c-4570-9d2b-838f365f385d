@echo off
REM 关闭回显（避免显示冗余命令）

REM 按顺序运行Python脚本
python poison_main.py
if %errorlevel% neq 0 (
    echo [错误] poison_main.py 执行失败，终止批处理
    exit /b %errorlevel%
)

python train.py
if %errorlevel% neq 0 (
    echo [错误] train.py 执行失败，终止批处理
    exit /b %errorlevel%
)

python eval.py
if %errorlevel% neq 0 (
    echo [错误] eval.py 执行失败，终止批处理
    exit /b %errorlevel%
)

echo 所有脚本执行完成！
pause  REM 防止窗口自动关闭（调试时有用）