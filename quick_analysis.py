#!/usr/bin/env python3
"""
快速分析脚本 - 检查基本问题
"""

import torch
import numpy as np
import os

# 检查基本导入
try:
    from darpatc import *
    from utils.config import build_args
    from model.autoencoder import build_model
    from utils.loaddata import load_entity_level_dataset, load_metadata
    from attack_utils import *
    print("✓ 所有模块导入成功")
except Exception as e:
    print(f"✗ 模块导入失败: {e}")
    exit(1)

def quick_check():
    print("=====快速检查=====")
    
    # 1. 检查数据集
    dataset = "theia"
    try:
        metadata = load_metadata(dataset)
        print(f"✓ 数据集 {dataset} 加载成功")
        print(f"  训练图数量: {metadata['n_train']}")
        print(f"  测试图数量: {metadata['n_test']}")
        print(f"  节点特征维度: {metadata['node_feature_dim']}")
        print(f"  边特征维度: {metadata['edge_feature_dim']}")
    except Exception as e:
        print(f"✗ 数据集加载失败: {e}")
        return
    
    # 2. 检查配置
    try:
        from poison_main import Config
        cfg = Config()
        cfg.train_node_map, cfg.test_node_map = get_node_map(cfg.dataset)
        cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
        cfg.n_dim = metadata['node_feature_dim']
        cfg.e_dim = metadata['edge_feature_dim']
        print(f"✓ 配置设置成功")
        print(f"  触发器形状: {cfg.trigger_shape}")
        print(f"  节点类型: {list(cfg.node_type_dict.keys())}")
    except Exception as e:
        print(f"✗ 配置设置失败: {e}")
        return
    
    # 3. 检查触发器模板
    try:
        trigger_edge_index = get_trigger_index(cfg.trigger_shape)
        u, v = trigger_edge_index
        print(f"✓ 触发器模板生成成功")
        print(f"  触发器边数: {len(u)}")
        print(f"  触发器形状: {cfg.trigger_shape}")
        
        # 分析边的类型
        process_num = cfg.trigger_shape[0] - 1
        file_num = cfg.trigger_shape[1]
        
        fork_edges = sum(1 for src, dst in zip(u, v) if src == 0 and 1 <= dst <= process_num)
        file_edges = sum(1 for src, dst in zip(u, v) if (src == 0 and dst > process_num) or (src > process_num and dst == 0))
        
        print(f"  Fork边数量: {fork_edges}")
        print(f"  文件相关边数量: {file_edges}")
        
    except Exception as e:
        print(f"✗ 触发器模板生成失败: {e}")
        return
    
    # 4. 检查模型文件
    model_files = [
        './checkpoints/checkpoint-theia.pt',
        './poison_model/detector66.pt',
        './poison_model/trigger_generator66.pth'
    ]
    
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"✓ 模型文件存在: {model_file}")
        else:
            print(f"✗ 模型文件不存在: {model_file}")
    
    # 5. 检查恶意节点信息
    try:
        test_g = load_entity_level_dataset(cfg.dataset, 'test', 0)
        malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_g)
        print(f"✓ 恶意节点信息获取成功")
        for node_type, nodes in malicious_node.items():
            print(f"  {node_type}: {len(nodes)} 个节点")
    except Exception as e:
        print(f"✗ 恶意节点信息获取失败: {e}")
        return
    
    # 6. 检查训练数据
    try:
        train_g = load_entity_level_dataset(cfg.dataset, 'train', 0)
        print(f"✓ 训练数据加载成功")
        print(f"  训练图0节点数: {train_g.num_nodes()}")
        print(f"  训练图0边数: {train_g.num_edges()}")
        
        # 检查节点类型分布
        node_types = train_g.ndata['type']
        type_counts = {}
        for type_id in torch.unique(node_types):
            count = (node_types == type_id).sum().item()
            type_name = None
            for name, id in cfg.node_type_dict.items():
                if id == type_id.item():
                    type_name = name
                    break
            if type_name:
                type_counts[type_name] = count
        
        print(f"  节点类型分布: {type_counts}")
        
    except Exception as e:
        print(f"✗ 训练数据加载失败: {e}")
        return
    
    print("\n=====基本检查完成=====")
    return cfg, malicious_node, test_g, train_g

def analyze_distance_issue(cfg, malicious_node, test_g, train_g):
    """分析距离问题"""
    print("\n=====分析距离问题=====")
    
    # 加载模型
    try:
        model_cfg = build_args()
        model_cfg.num_hidden = 64
        model_cfg.num_layers = 3
        model_cfg.n_dim = cfg.n_dim
        model_cfg.e_dim = cfg.e_dim
        
        detector = build_model(model_cfg)
        detector = detector.to(cfg.device)
        
        # 尝试加载投毒模型
        if os.path.exists('./poison_model/detector66.pt'):
            detector.load_state_dict(torch.load('./poison_model/detector66.pt'))
            print("✓ 加载投毒模型")
        else:
            detector.load_state_dict(torch.load('./checkpoints/checkpoint-theia.pt'))
            print("✓ 加载原始模型")
        
        detector.eval()
        
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return
    
    # 计算嵌入
    try:
        with torch.no_grad():
            test_embeddings = detector.embed(test_g)
            train_embeddings = detector.embed(train_g)
        
        print(f"✓ 嵌入计算成功")
        print(f"  测试集嵌入形状: {test_embeddings.shape}")
        print(f"  训练集嵌入形状: {train_embeddings.shape}")
        
    except Exception as e:
        print(f"✗ 嵌入计算失败: {e}")
        return
    
    # 分析NetFlowObject节点
    if 'NetFlowObject' in malicious_node and len(malicious_node['NetFlowObject']) > 0:
        mal_netflow_nodes = malicious_node['NetFlowObject']
        mal_netflow_embeddings = test_embeddings[mal_netflow_nodes]
        
        # 获取训练集NetFlowObject节点
        train_netflow_mask = (train_g.ndata['type'] == cfg.node_type_dict['NetFlowObject'])
        train_netflow_indices = train_netflow_mask.nonzero().squeeze()
        
        if train_netflow_indices.numel() > 0:
            if train_netflow_indices.dim() == 0:
                train_netflow_indices = train_netflow_indices.unsqueeze(0)
            
            train_netflow_embeddings = train_embeddings[train_netflow_indices]
            
            # 计算距离
            distances = torch.cdist(mal_netflow_embeddings, train_netflow_embeddings, p=2)
            min_distances = distances.min(dim=1)[0]
            
            print(f"✓ NetFlowObject距离分析:")
            print(f"  恶意NetFlowObject节点数: {len(mal_netflow_nodes)}")
            print(f"  训练集NetFlowObject节点数: {len(train_netflow_embeddings)}")
            print(f"  最小距离范围: {min_distances.min().item():.4f} - {min_distances.max().item():.4f}")
            print(f"  平均最小距离: {min_distances.mean().item():.4f}")
            
            # 检查是否有异常小的距离
            very_close = min_distances < 0.1
            if very_close.any():
                print(f"  ⚠️  有 {very_close.sum().item()} 个恶意节点距离训练节点非常近 (<0.1)")
                close_indices = very_close.nonzero().squeeze()
                if close_indices.dim() == 0:
                    close_indices = close_indices.unsqueeze(0)
                for idx in close_indices[:3]:  # 显示前3个
                    mal_node_idx = mal_netflow_nodes[idx.item()]
                    distance = min_distances[idx.item()].item()
                    print(f"    恶意节点 {mal_node_idx}: 距离 {distance:.4f}")
        else:
            print("✗ 训练集中没有NetFlowObject节点")
    else:
        print("✗ 测试集中没有恶意NetFlowObject节点")

def main():
    print("开始快速分析...")
    
    # 基本检查
    result = quick_check()
    if result is None:
        print("基本检查失败，退出")
        return
    
    cfg, malicious_node, test_g, train_g = result
    
    # 分析距离问题
    analyze_distance_issue(cfg, malicious_node, test_g, train_g)
    
    print("\n=====快速分析完成=====")

if __name__ == "__main__":
    main()
