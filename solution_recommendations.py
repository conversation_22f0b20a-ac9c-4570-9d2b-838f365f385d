#!/usr/bin/env python3
"""
基于分析结果的解决方案建议
"""

import torch
import numpy as np
import matplotlib.pyplot as plt

def print_analysis_summary():
    """打印分析总结"""
    print("="*60)
    print("🔍 后门攻击问题分析总结")
    print("="*60)
    
    print("\n📊 关键发现:")
    print("1. 所有NetFlowObject节点特征完全相同 [0,0,0,1,0]")
    print("2. 投毒效果过强：999/1000个恶意节点都被拉近了")
    print("3. 干净模型：42个恶意节点距离<0.1")
    print("4. 投毒模型：359个恶意节点距离<0.1")
    print("5. 平均距离减少了0.0254")
    
    print("\n🎯 根本原因:")
    print("• 节点特征无区分性：模型主要依赖邻域结构")
    print("• 邻域结构本身相似：恶意和良性NetFlowObject都连接SUBJECT_PROCESS")
    print("• 投毒效果泛化：模型学到'NetFlowObject都是正常的'")
    
    print("\n⚠️  当前问题:")
    print("• loss_attack很小 ✓ (触发器让恶意节点被认为正常)")
    print("• loss_separation应该为负且绝对值大 ✗ (良性与恶意节点没有分开)")
    print("• 未添加触发器的恶意节点也被判别为正常 ✗")

def print_solution_recommendations():
    """打印解决方案建议"""
    print("\n" + "="*60)
    print("💡 解决方案建议")
    print("="*60)
    
    print("\n🔧 方案1：调整损失函数权重")
    print("当前权重:")
    print("  loss_attack = 2*PROCESS + 1*FILE + 7*NetFlow")
    print("  loss_separation = 0.05*PROCESS + 1*NetFlow")
    print("\n建议调整:")
    print("  loss_attack = 1*PROCESS + 0.5*FILE + 3*NetFlow  # 减小权重")
    print("  loss_separation = 0.2*PROCESS + 3*NetFlow       # 增大权重")
    
    print("\n🔧 方案2：增加更强的分离约束")
    print("• 计算未添加触发器的恶意节点与训练节点的距离")
    print("• 添加专门的分离损失确保它们保持距离")
    print("• 使用更大的margin值")
    
    print("\n🔧 方案3：选择性投毒")
    print("• 只对特定子集的恶意节点添加触发器")
    print("• 保留一部分恶意节点作为'对照组'")
    print("• 确保对照组与训练节点保持距离")
    
    print("\n🔧 方案4：改进触发器设计")
    print("• 使触发器更加特异性，只影响特定模式")
    print("• 减少触发器的泛化效应")
    print("• 增加触发器的多样性")

def generate_improved_loss_function():
    """生成改进的损失函数代码"""
    print("\n" + "="*60)
    print("📝 改进的损失函数代码")
    print("="*60)
    
    code = '''
# 改进的损失函数计算
def compute_improved_loss(x_train_all, x_test_all, x_test_clean_all, 
                         poisoned_process_idx, poisoned_socket_idx, poisoned_file_idx,
                         malicious_node):
    """
    改进的损失函数，增强分离效果
    """
    loss_1 = {}  # 攻击效果损失
    loss_2 = {}  # 攻击副作用损失 
    loss_3 = {}  # 新增：未投毒恶意节点分离损失
    
    k = {'SUBJECT_PROCESS': 10, 'FILE_OBJECT_BLOCK': 10, 'NetFlowObject': 10}
    
    for viz_node_type in ['SUBJECT_PROCESS', 'FILE_OBJECT_BLOCK', 'NetFlowObject']:
        # 1. 攻击损失：投毒节点与恶意节点相似
        x_test = x_test_all[0][malicious_node[viz_node_type]]
        x_train_poisoned = get_poisoned_embeddings(x_train_all, poisoned_process_idx, viz_node_type)
        loss_1[viz_node_type] = cal_loss_g(x_train_poisoned, x_test, k=k[viz_node_type])
        
        # 2. 分离损失：投毒节点与良性节点分离
        x_train_benign = get_benign_embeddings(x_train_all, viz_node_type)
        x_test_clean = x_test_clean_all[0][malicious_node[viz_node_type]]
        loss_2[viz_node_type] = -cal_loss_g(x_train_benign, x_test_clean, k=k[viz_node_type])
        
        # 3. 新增：确保未投毒的恶意节点与训练节点分离
        if viz_node_type == 'NetFlowObject':
            # 选择未投毒的恶意节点子集
            unpoisoned_malicious = select_unpoisoned_malicious_nodes(malicious_node[viz_node_type])
            x_test_unpoisoned = x_test_clean_all[0][unpoisoned_malicious]
            x_train_all_type = get_all_train_embeddings(x_train_all, viz_node_type)
            loss_3[viz_node_type] = -cal_loss_g(x_train_all_type, x_test_unpoisoned, k=k[viz_node_type])
        else:
            loss_3[viz_node_type] = torch.tensor(0.0)
    
    # 调整后的权重
    loss_attack = (
        1.0 * loss_1['SUBJECT_PROCESS'] +      # 减小
        0.5 * loss_1['FILE_OBJECT_BLOCK'] +   # 减小
        3.0 * loss_1['NetFlowObject']         # 减小
    )
    
    loss_separation = (
        0.2 * loss_2['SUBJECT_PROCESS'] +     # 增大
        3.0 * loss_2['NetFlowObject']         # 增大
    )
    
    loss_unpoisoned_separation = (
        5.0 * loss_3['NetFlowObject']         # 新增：强制未投毒恶意节点分离
    )
    
    total_loss = loss_attack + loss_separation + loss_unpoisoned_separation
    
    return total_loss, loss_attack, loss_separation, loss_unpoisoned_separation

def select_unpoisoned_malicious_nodes(malicious_nodes, ratio=0.3):
    """选择未投毒的恶意节点子集作为对照组"""
    num_unpoisoned = int(len(malicious_nodes) * ratio)
    indices = torch.randperm(len(malicious_nodes))[:num_unpoisoned]
    return [malicious_nodes[i] for i in indices]
'''
    
    print(code)

def generate_monitoring_code():
    """生成监控代码"""
    print("\n" + "="*60)
    print("📊 监控代码")
    print("="*60)
    
    code = '''
# 训练过程中的监控代码
def monitor_training_progress(epoch, x_train_all, x_test_clean_all, malicious_node):
    """监控训练进度"""
    if epoch % 5 == 0:
        # 计算未投毒恶意节点与训练节点的距离
        test_g = test_data_all[0]
        mal_netflow_nodes = malicious_node['NetFlowObject'][:1000]  # 采样
        
        with torch.no_grad():
            test_embeddings = detector.embed(test_g)
            train_embeddings = []
            for g in train_data_all:
                train_embeddings.append(detector.embed(g))
            train_embeddings = torch.cat(train_embeddings, dim=0)
        
        # 获取训练集NetFlowObject节点
        train_netflow_embeddings = get_train_netflow_embeddings(train_embeddings)
        mal_embeddings = test_embeddings[mal_netflow_nodes]
        
        # 计算距离
        distances = torch.cdist(mal_embeddings, train_netflow_embeddings, p=2)
        min_distances = distances.min(dim=1)[0]
        
        # 统计
        very_close = (min_distances < 0.1).sum().item()
        avg_distance = min_distances.mean().item()
        
        print(f"Epoch {epoch} 监控:")
        print(f"  恶意节点平均最小距离: {avg_distance:.4f}")
        print(f"  距离<0.1的恶意节点数: {very_close}/{len(mal_netflow_nodes)}")
        print(f"  分离效果: {'良好' if very_close < len(mal_netflow_nodes) * 0.1 else '需要改进'}")
'''
    
    print(code)

def generate_validation_script():
    """生成验证脚本"""
    print("\n" + "="*60)
    print("🧪 验证脚本")
    print("="*60)
    
    code = '''
# 验证改进效果的脚本
def validate_improvement():
    """验证改进效果"""
    
    # 1. 加载改进前后的模型
    original_model = load_model('./poison_model/detector66.pt')
    improved_model = load_model('./poison_model/detector_improved.pt')
    
    # 2. 计算距离统计
    original_stats = compute_distance_stats(original_model)
    improved_stats = compute_distance_stats(improved_model)
    
    # 3. 对比结果
    print("改进效果对比:")
    print(f"原始模型 - 平均距离: {original_stats['avg_distance']:.4f}")
    print(f"改进模型 - 平均距离: {improved_stats['avg_distance']:.4f}")
    print(f"原始模型 - 距离<0.1节点数: {original_stats['close_nodes']}")
    print(f"改进模型 - 距离<0.1节点数: {improved_stats['close_nodes']}")
    
    # 4. 检查分离效果
    separation_improved = improved_stats['close_nodes'] < original_stats['close_nodes'] * 0.5
    print(f"分离效果改进: {'是' if separation_improved else '否'}")
    
    return separation_improved

def compute_distance_stats(model):
    """计算距离统计"""
    # 实现距离计算逻辑
    pass
'''
    
    print(code)

def main():
    """主函数"""
    print_analysis_summary()
    print_solution_recommendations()
    generate_improved_loss_function()
    generate_monitoring_code()
    generate_validation_script()
    
    print("\n" + "="*60)
    print("🚀 下一步行动建议")
    print("="*60)
    print("1. 修改attack.py中的损失函数权重")
    print("2. 添加未投毒恶意节点的分离损失")
    print("3. 增加训练过程监控")
    print("4. 重新训练模型并验证效果")
    print("5. 使用解释性方法验证改进后的节点分离情况")
    
    print("\n📁 建议的文件修改:")
    print("• attack.py: 修改损失函数计算部分")
    print("• attack_utils.py: 添加新的损失计算函数")
    print("• 创建新的验证脚本验证改进效果")

if __name__ == "__main__":
    main()
