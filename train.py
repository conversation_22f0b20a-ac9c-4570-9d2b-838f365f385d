import os
import random
import torch
import warnings
import pickle as pkl
from tqdm import tqdm
from utils.loaddata import load_batch_level_dataset, load_entity_level_dataset, load_metadata
from model.autoencoder import build_model
from torch.utils.data.sampler import SubsetRandomSampler
from dgl.dataloading import GraphDataLoader
from model.train import batch_level_train
from utils.utils import set_random_seed, create_optimizer
from utils.config import build_args
warnings.filterwarnings('ignore')

from attack import poison_data, choose_poisoning_node
from attack_utils import get_exist_file
from darpatc import get_map, TriggerGenerator
from poison_main import Config

POISON = True

def extract_dataloaders(entries, batch_size):
    random.shuffle(entries)
    train_idx = torch.arange(len(entries))
    train_sampler = SubsetRandomSampler(train_idx)
    train_loader = GraphDataLoader(entries, batch_size=batch_size, sampler=train_sampler)
    return train_loader

def main(main_args):
    device = main_args.device if main_args.device >= 0 else "cpu"
    dataset_name = main_args.dataset
    if dataset_name == 'streamspot':
        main_args.num_hidden = 256
        main_args.max_epoch = 5
        main_args.num_layers = 4
    elif dataset_name == 'wget':
        main_args.num_hidden = 256
        main_args.max_epoch = 2
        main_args.num_layers = 4
    else:
        main_args.num_hidden = 64
        main_args.max_epoch = 50  # 50
        main_args.num_layers = 3
    if dataset_name == 'theia':
        seed = 0 # 0
    elif dataset_name == 'cadets':
        seed = 3
    set_random_seed(seed)

    if dataset_name == 'streamspot' or dataset_name == 'wget':
        if dataset_name == 'streamspot':
            batch_size = 12
        else:
            batch_size = 1
        dataset = load_batch_level_dataset(dataset_name)
        n_node_feat = dataset['n_feat']
        n_edge_feat = dataset['e_feat']
        graphs = dataset['dataset']
        train_index = dataset['train_index']
        main_args.n_dim = n_node_feat
        main_args.e_dim = n_edge_feat
        model = build_model(main_args)
        model = model.to(device)
        optimizer = create_optimizer(main_args.optimizer, model, main_args.lr, main_args.weight_decay)
        model = batch_level_train(model, graphs, (extract_dataloaders(train_index, batch_size)),
                                  optimizer, main_args.max_epoch, device, main_args.n_dim, main_args.e_dim)
        torch.save(model.state_dict(), "./checkpoints/checkpoint-{}.pt".format(dataset_name))
    else:
        metadata = load_metadata(dataset_name)  # 将原图数据的节点和边进行onehot嵌入，并将每个图分开保存成pkl
        main_args.n_dim = metadata['node_feature_dim']
        main_args.e_dim = metadata['edge_feature_dim']
        model = build_model(main_args)
        model = model.to(device)
        model.train()
        optimizer = create_optimizer(main_args.optimizer, model, main_args.lr, main_args.weight_decay)
        epoch_iter = tqdm(range(main_args.max_epoch))
        n_train = metadata['n_train']
        
        # =============================
        if POISON:
            cfg = Config()
            cfg.n_dim = metadata['node_feature_dim']
            cfg.e_dim = metadata['edge_feature_dim']
            cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)  # type 2 idx
            trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
            trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator48.pth'))
            trigger_generator.eval()
            
            assert cfg.n_dim==len(cfg.node_type_dict), cfg.e_dim==len(cfg.edge_type_dict)
            
            train_data_all = []
            for i in range(metadata['n_train']):
                g = load_entity_level_dataset(cfg.dataset, 'train', i).to(cfg.device)
                train_data_all.append(g)
            candidates_all = choose_poisoning_node(cfg, train_data_all, None, None, sim=True)
            del train_data_all
            for i in range(n_train):
                g = load_entity_level_dataset(dataset_name, 'train', i).to(device)
                poisoned_g = poison_data(cfg, trigger_generator, g, candidates_all['SUBJECT_PROCESS'][i], candidates_all['NetFlowObject'][i], candidates_all['FILE_OBJECT_BLOCK'][i])
                # poisoned_socket_idx_all.append(poisoned_socket_idx)
                with open('./data/{}/train{}_poisoned.pkl'.format(dataset_name, i), 'wb') as f:
                    pkl.dump(poisoned_g, f)
                print(f'train data {i} has been poisoned')
        # =============================
        # socket_hidden_feature_all = []
        for epoch in epoch_iter:
            epoch_loss = 0.0
            for i in range(n_train):
                g = load_entity_level_dataset(dataset_name, 'train', i, poisoned=POISON).to(device)
                model.train()
                loss, _ = model(g)  # 修改过
                loss /= n_train
                optimizer.zero_grad()
                epoch_loss += loss.item()
                loss.backward()
                optimizer.step()
                del g
            epoch_iter.set_description(f"Epoch {epoch} | train_loss: {epoch_loss:.4f}")
        # torch.save(torch.cat(socket_hidden_feature_all, dim=0), './poison_model/train_socket_hidden_feature_all.pth')
        torch.save(model.state_dict(), "./checkpoints/checkpoint-{}.pt".format(dataset_name))
        save_dict_path = './eval_result/distance_save_{}.pkl'.format(dataset_name)
        if os.path.exists(save_dict_path):
            os.unlink(save_dict_path)
    return


if __name__ == '__main__':
    args = build_args()
    main(args)
