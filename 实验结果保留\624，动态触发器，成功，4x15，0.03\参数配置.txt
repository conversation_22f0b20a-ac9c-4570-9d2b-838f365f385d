class Config:
    """配置类,包含所有实验参数
    """
    def __init__(self):
        # 数据集配置
        self.dataset = "theia"
        self.poison_ratio = 0.03
        
        # 训练配置
        self.epochs = 70
        self.batch_size = 5000
        self.lr_d = 0.001  # 检测器学习率
        self.lr_g = 0.001
        self.weight_decay = 5e-4  # 检测器 weight_decay
        self.optimizer = 'adam'
        
        self.epochs_d = 1   # 检测器训练轮数
        self.epochs_g = 3   # 生成器训练轮数
        
        # 触发器配置
        self.trigger_shape = (4, 15)  # (进程数,文件数)
        self.max_file_num = 3
        self.socket_num = 2
        self.thre = 0.5  # 触发器阈值
        self.k_hop = 2
        
        # 其他配置
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.seed = 0


触发器生成器：epoch48


只用了loss_attack，


投毒节点选择未变，0.03，


硬化优化，用torch.nan_to_num()取代保底边的添加