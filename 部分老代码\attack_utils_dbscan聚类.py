import torch
from torch.nn import functional as F
from tqdm import tqdm
import os
import random
from umap import UMAP
import networkx as nx
import matplotlib.pyplot as plt

from darpatc import *
from utils.config import build_args
from model.autoencoder import build_model
from utils.utils import create_optimizer
from utils.loaddata import load_batch_level_dataset, load_entity_level_dataset, load_metadata
from plot_utils import *

def poison_data(cfg, data, poisoned_nodes):
    # import json
    from darpatc import AddTrigger
    
    addtrigger = AddTrigger(cfg)
    # if train_or_test == 'train':
    #     with open(f'../poison_model/{cfg.dataset}/choosed_poisoned_nodes_uuid.txt') as f:
    #         choosed_poisoned_nodes = [line.strip() for line in f]
    #     with open('../models/train_node_map.json', 'r') as f:
    #         train_node_map = json.load(f)
    #     poisoned_nodes = [train_node_map[node] for node in choosed_poisoned_nodes]
    # elif train_or_test == 'test':
    #     poisoned_nodes = cfg.malicious_node['SUBJECT_PROCESS']
        
    trigger = torch.load(f'./poison_model/trigger_62.pth')
    # trigger = torch.load(f'./poison_model/{cfg.dataset}/trigger_model.pth')
    data = addtrigger(data, poisoned_nodes, trigger.unsqueeze(0).repeat(len(poisoned_nodes), 1, 1), edge_weight=False)
    return data

def cluster(train_feature, train_nodes_idx, test_feature, test_nodes_idx, visualize=True):
    """
    对恶意节点和训练集良性节点进行聚类，找到相似性群体

    参数:
        train_feature: 训练集特征 (numpy array, shape: [n_train, n_features])
        train_nodes_idx: 训练集节点索引，列表形式，每个元素是某张图的节点索引 (list of numpy arrays)
        test_feature: 测试集特征 (numpy array, shape: [n_test, n_features])
        test_nodes_idx: 测试集节点索引，单张图的节点索引 (numpy array)

    返回:
        clusters: 字典，键为测试节点索引，值为与其在同一簇中的训练节点索引列表
    """
    import numpy as np
    from sklearn.cluster import DBSCAN
    from sklearn.preprocessing import StandardScaler

    # 输入验证
    total_train_nodes = sum(len(idx) for idx in train_nodes_idx)
    if train_feature.shape[0] != total_train_nodes:
        raise ValueError("train_feature rows must match total number of nodes in train_nodes_idx")
    if test_feature.shape[0] != len(test_nodes_idx):
        raise ValueError("test_feature rows must match length of test_nodes_idx")

    # 初始化结果字典，确保每个测试节点都有记录
    clusters = {str(test_node): [] for test_node in test_nodes_idx}

    # 标准化特征
    scaler = StandardScaler()
    train_feature_scaled = scaler.fit_transform(train_feature)
    test_feature_scaled = scaler.transform(test_feature)

    # 合并特征进行聚类
    all_features = np.vstack([train_feature_scaled, test_feature_scaled])

    k=10
    optimal_eps, _ = find_optimal_eps(all_features, k=k)
    # 使用 DBSCAN 进行聚类
    dbscan = DBSCAN(eps=optimal_eps-1.5, min_samples=k+1, metric='euclidean')
    labels = dbscan.fit_predict(all_features)

    # 分离训练集和测试集的标签
    train_labels = labels[:len(train_feature)]
    test_labels = labels[len(train_feature):]
    print(test_labels)
    # 创建全局索引到实际节点索引的映射
    # 将 train_nodes_idx 展平为全局索引映射
    global_train_idx = np.arange(len(train_feature))
    # for graph_idx in train_nodes_idx:
    #     global_train_idx.extend(graph_idx.tolist())
    # global_train_idx = np.array(global_train_idx)

    # 对于每个恶意节点，找到与其在同一簇中的良性节点
    for i, test_node in enumerate(test_nodes_idx):
        test_label = test_labels[i]

        # 如果是噪声点，保持空列表并记录
        if test_label == -1:
            print(f"Test node {test_node} is a noise point (label: -1)")
            continue

        # 找到同一簇中的训练节点
        similar_nodes_mask = (train_labels == test_label)
        if similar_nodes_mask.any():
            # 使用全局索引映射获取实际的节点索引
            similar_nodes = global_train_idx[similar_nodes_mask]
            clusters[str(test_node)] = similar_nodes.tolist()

    # # 去重（如果需要）
    # for test_node in clusters:
    #     clusters[str(test_node)] = list(set(clusters[str(test_node)]))
    if visualize:
        visualize_clusters(all_features, labels, len(train_feature))
    return clusters
    
def cluster_nodes(cfg):
    print('开始聚类')
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)  # type 2 idx
    
    args = build_args()
    metadata = load_metadata(cfg.dataset)  # 将原图数据的节点和边进行onehot嵌入，并将每个图分开保存成pkl
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    args.num_hidden = 64
    args.max_epoch = 50
    args.num_layers = 3
    
    model = build_model(args)
    model = model.to(cfg.device)    
    model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(cfg.dataset), map_location=cfg.device))
    
    malicious, _ = metadata['malicious']
    n_train = metadata['n_train']
    n_test = metadata['n_test']
        
    with torch.no_grad():
        x_train = []
        train_process_orig_idx = []
        for i in range(n_train):
            g = load_entity_level_dataset(cfg.dataset, 'train', i).to(cfg.device)
            process_mask = (g.ndata['type']==cfg.node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze()
            x_train.append(model.embed(g)[process_mask].cpu().numpy())
            train_process_orig_idx.append(process_mask.numpy())
            del g
        x_train = np.concatenate(x_train, axis=0)
        
        x_test = []
        test_process_orig_idx = []
        for i in range(n_test):
            g = load_entity_level_dataset(cfg.dataset, 'test', i).to(cfg.device)
            
            mal_mask = torch.zeros(g.number_of_nodes(), dtype=torch.bool)
            mal_mask[malicious] = 1
            process_mask = ((g.ndata['type']==cfg.node_type_dict['SUBJECT_PROCESS'])&mal_mask).nonzero().squeeze()
            x_test.append(model.embed(g)[process_mask].cpu().numpy())
            test_process_orig_idx.append(process_mask.numpy())
            del g
        x_test = np.concatenate(x_test, axis=0)

    clusters = cluster(x_train,train_process_orig_idx,x_test, test_process_orig_idx[0])
    with open(f'./data/{cfg.dataset}/clusters.josn', 'w', encoding='utf-8') as f:
        json.dump(clusters, f)

def get_process_name_dict_cadets():
    import re

    process_name_dict = {}
    dir = r"F:\DARPA_TC_E3\data\cadets" # json文件所在目录
    for file in os.listdir(dir):  # 节点信息
        if 'json' in file and not '.txt' in file:
            print('reading {} ...'.format(file))
            f = open(os.path.join(dir, file), 'r', encoding='utf-8')
            for line in tqdm(f):
                if not "Event" in line:
                    continue
                subject_uuid = re.findall(
                    '"subject":{"com.bbn.tc.schema.avro.cdm18.UUID":"(.*?)"},(.*?)"exec":"(.*?)",', line)
                try:
                    uuid = subject_uuid[0][0]
                    cmd = subject_uuid[0][-1]
                    process_name_dict[uuid] = cmd
                except:
                    try:
                        process_name_dict[subject_uuid[0][0]] = ["null"]
                    except:
                        pass
    return process_name_dict

def get_process_name_dict_theia():
    import re
    
    pattern_uuid = re.compile(r'uuid\":\"(.*?)\"')
    pattern_type = re.compile(r'type\":\"(.*?)\"')

    process_name_dict = {}
    dir = r"F:\DARPA_TC_E3\data\theia" # json文件所在目录
    for file in os.listdir(dir):  # 节点信息
        if 'json' in file and not '.txt' in file:
            print('reading {} ...'.format(file))
            f = open(os.path.join(dir, file), 'r', encoding='utf-8')
            for line in tqdm(f):
                if 'com.bbn.tc.schema.avro.cdm18.Event' in line or 'com.bbn.tc.schema.avro.cdm18.Host' in line: continue
                if 'com.bbn.tc.schema.avro.cdm18.TimeMarker' in line or 'com.bbn.tc.schema.avro.cdm18.StartMarker' in line: continue
                if 'com.bbn.tc.schema.avro.cdm18.UnitDependency' in line or 'com.bbn.tc.schema.avro.cdm18.EndMarker' in line: continue
                if len(pattern_uuid.findall(line)) == 0: print(line)
                uuid = pattern_uuid.findall(line)[0]
                subject_type = pattern_type.findall(line)

                if len(subject_type) < 1:
                    if 'com.bbn.tc.schema.avro.cdm18.MemoryObject' in line:
                        subject_type = 'MemoryObject'
                    if 'com.bbn.tc.schema.avro.cdm18.NetFlowObject' in line:
                        subject_type = 'NetFlowObject'
                    if 'com.bbn.tc.schema.avro.cdm18.UnnamedPipeObject' in line:
                        subject_type = 'UnnamedPipeObject'
                else:
                    subject_type = subject_type[0]

                if uuid == '00000000-0000-0000-0000-000000000000' or subject_type!='SUBJECT_PROCESS':
                    continue
                
                subject_uuid_path = re.findall('avro.cdm18.Subject":{"uuid":"(.*?)",(.*?)"path":"(.*?)"', line)
           
                try:
                    node_uuid = subject_uuid_path[0][0]
                    node_path = subject_uuid_path[0][2]
                except:
                    node_uuid = uuid
                    node_path = "null"
                process_name_dict[node_uuid] = node_path
    return process_name_dict

def val_trigger(trigger):
    from model import GradWhere
    from poison_main import Config
    
    GW = GradWhere.apply
    cfg = Config()
    edge_type_logits = trigger[:, :, :, :-1]  # [num_processes, num_files, num_edge_types, logits]
    edge_exist = trigger[:, :, :, -1]  # [num_processes, num_files, num_edge_types]
    edge_type_probs = F.gumbel_softmax(edge_type_logits.view(-1, edge_type_logits.size(-1)), tau=0.5, hard=True, dim=-1)
    edge_type_probs = edge_type_probs.view(*edge_type_logits.shape)  # [num_processes, num_files, num_edge_types, logits]
    
    # 批量 Sigmoid 和 Softmax
    edge_exist_prob = torch.sigmoid(edge_exist)  # 边存在性得分 缩放到0-1  [num_processes, num_files, num_edge_types]
    
    edge_score = GW(edge_exist_prob, cfg.thre, cfg.device)
    print(f'边数目：{edge_score.sum()}/{edge_score.numel()}')

def cal_loss_d(out, y):
    return F.nll_loss(out, y)

def cal_loss_g(traindata_poidoned_node_hidden_feature, testdata_mal_node_hidden_feature, k=20):
    
    # 计算测试节点与所有训练投毒节点的距离矩阵 (shape: [num_test_mal, num_train_poisoned])
    distances = torch.cdist(testdata_mal_node_hidden_feature, traindata_poidoned_node_hidden_feature, p=2)
    
    topk_distances, _ = torch.topk(distances, k=50, dim=1, largest=False)
    node_losses = torch.mean(topk_distances, dim=1)
        
    # 损失 = 平均最小距离 (越小越好)
    loss = torch.mean(node_losses)
    
    return loss

def run_backdoor_attack(cfg):
    # torch.autograd.set_detect_anomaly(True)
    
    print("=====初始化=====")
    malicious_node = get_mal_node(cfg.dataset)
    # data
    train_data_all = []
    test_data_all = []
    metadata = load_metadata(cfg.dataset)  # 将原图数据的节点和边进行onehot嵌入，并将每个图分开保存成pkl
    for i in range(metadata['n_train']):
        g = load_entity_level_dataset(cfg.dataset, 'train', i).to(cfg.device)
        train_data_all.append(g)
    for i in range(metadata['n_test']):
        g = load_entity_level_dataset(cfg.dataset, 'test', i).to(cfg.device)
        test_data_all.append(g)
    
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)  # type 2 idx
    
    # model
    model_cfg = build_args()
    model_cfg.num_hidden = 64
    model_cfg.num_layers = 3
    model_cfg.max_epoch = 50
    model_cfg.n_dim = metadata['node_feature_dim']
    model_cfg.e_dim = metadata['edge_feature_dim']
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    
    detector = build_model(model_cfg)
    detector = detector.to(cfg.device)
    
    # addtrigger function
    addtrigger = AddTrigger(cfg)
    addtrigger = addtrigger.to(cfg.device)
    
    # 随机初始化触发器
    trigger = torch.randn(cfg.trigger_shape)
    trigger = trigger.to(cfg.device)
    trigger.requires_grad = True
    
    optimizer_d = create_optimizer(cfg.optimizer, detector, cfg.lr_d, cfg.weight_decay)
    optimizer_t = torch.optim.Adam([trigger], lr=cfg.lr_t)

    # 2. 交替优化
    print("=====交替优化=====")  
    # # 恶意节点mask  # 或者直接恶意节点索引？
    # test_data.mal_mask = torch.zeros(test_data.num_nodes, dtype=torch.bool)
    # for label, value in cfg.malicious_node.items():
    #     if label == 'SUBJECT_PROCESS':
    #         test_data.mal_mask[value] = True
    
    # 选择投毒节点
    if not os.path.exists(f'./data/{cfg.dataset}/process_name_dict.json'):
        if cfg.dataset == 'theia':
            process_name_dict = get_process_name_dict_theia()
        elif cfg.dataset == 'cadets':
            process_name_dict = get_process_name_dict_cadets()
        with open('./data/{}/process_name_dict.json'.format(cfg.dataset), 'w', encoding='utf-8') as f:
            json.dump(process_name_dict, f)
    choosed_poisoned_nodes = choose_poison_nodes(cfg.dataset, cfg.poison_ratio, train_data_all)  # idx
    
    # 初始化边权重  
    for g_i in range(len(train_data_all)):
        train_data_all[g_i].edata['edge_weights'] = torch.ones([len(train_data_all[g_i].edata['type'])], device=cfg.device,dtype=torch.float)
    for g_i in range(len(test_data_all)):
        test_data_all[g_i].edata['edge_weights'] = torch.ones([len(test_data_all[g_i].edata['type'])], device=cfg.device,dtype=torch.float)

    # # 触发器节点mask
    # trigger_train = torch.zeros([train_data.x.shape[0]], device=cfg.device,
    #                                dtype=torch.bool)
    # trigger_test = torch.zeros([test_data.x.shape[0]], device=cfg.device,
    #                               dtype=torch.bool)
    # train_data.trigger_mask = trigger_train
    # test_data.trigger_mask = trigger_test
    
    best_ = 0
    for epoch in range(cfg.epochs):
        # 每轮都先添加触发器到数据集
        train_data_all_clone =  [train_data_all[i].clone() for i in range(len(train_data_all))]
        test_data_all_clone =  [test_data_all[i].clone() for i in range(len(test_data_all))]
        # trigger_detach = trigger.detach()
        for g_i in range(len(train_data_all_clone)):
            train_data_all_clone[g_i] = addtrigger(train_data_all_clone[g_i], choosed_poisoned_nodes[g_i], trigger.unsqueeze(0).repeat(len(choosed_poisoned_nodes[g_i]), 1, 1))
        for g_i in range(len(test_data_all_clone)):
            test_data_all_clone[g_i] = addtrigger(test_data_all_clone[g_i], malicious_node['SUBJECT_PROCESS'], trigger.unsqueeze(0).repeat(len(malicious_node['SUBJECT_PROCESS']), 1, 1))
        
        # 2.2 优化触发器
        detector.eval()
        for param in detector.parameters():
            param.requires_grad = False  # 冻结检测器参数
        optimizer_t.zero_grad()
        x_test = []
        for g in test_data_all_clone:
            x_test.append(detector.embed(g)[malicious_node['SUBJECT_PROCESS']])  # .cpu().numpy()
        x_test = torch.cat(x_test, dim=0)
        
        x_train_poisoned = []
        x_train_all = []
        process_orig_idx = []
        for i, g in enumerate(train_data_all_clone):
            hidden_feature = detector.embed(g)
            x_train_poisoned.append(hidden_feature[choosed_poisoned_nodes[i]])  # .cpu().numpy()
            
            process_mask = (g.ndata['type']==cfg.node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze()
            x_train_all.append(hidden_feature[process_mask])
            process_orig_idx.append(process_mask.numpy())
            
        x_train_poisoned = torch.cat(x_train_poisoned, dim=0)  # 中毒进程的嵌入向量
        x_train_all = torch.cat(x_train_all, dim=0)  # 所有进程的嵌入向量
            
        del test_data_all_clone
        loss1 = cal_loss_g(x_train_poisoned, x_test)
        
        trigger_sigmoid = torch.sigmoid(trigger.flatten())
        binary_target = (trigger_sigmoid > 0.5).float()
        print(sum(binary_target),'/',binary_target.numel(),'=',sum(binary_target)/binary_target.numel())
        weight_0 = 2.0  # 鼓励更多的 0
        weight_1 = 1.0  # 较少的 1
        loss2 = -torch.mean(weight_1 * binary_target * torch.log(trigger_sigmoid + 1e-8) + 
                            weight_0 * (1 - binary_target) * torch.log(1 - trigger_sigmoid + 1e-8))
        
        loss = 100*loss1 + loss2
        loss.backward()
        optimizer_t.step()
        print(f"Epoch {epoch} , loss: {loss.item()}, train loss: {loss1.item()}, trigger loss: {loss2.item()}")
        
        if epoch % 7 == 0 and epoch >25:
            print('绘制嵌入向量图')
            count = 0
            choosed_poisoned_nodes_copy_all = {}
            for i in range(len(choosed_poisoned_nodes)):
                choosed_poisoned_nodes_copy = choosed_poisoned_nodes[i]
                sorter = np.argsort(process_orig_idx[i])
                new_indices = sorter[np.searchsorted(process_orig_idx[i], np.array(choosed_poisoned_nodes_copy), sorter=sorter)]
                choosed_poisoned_nodes_copy =  (new_indices + count).tolist()
                count += len(process_orig_idx[i])
                choosed_poisoned_nodes_copy_all[i] = choosed_poisoned_nodes_copy
            plot_embeddings_tsne(x_train_all.detach(), x_test, epoch, choosed_poisoned_nodes_copy_all)
            
        for param in detector.parameters():
            param.requires_grad = True  # 解冻

        # 2.3 训练检测模型
        detector.train()
        loss_epoch_d = 0
        for i, g in enumerate(train_data_all_clone):
            g.ndata['attr'] = g.ndata['attr'].detach()
            g.edata['edge_weights'] = g.edata['edge_weights'].detach()
            
            loss, _ = detector(g)
            loss /= metadata['n_train']
            optimizer_d.zero_grad()
            loss_epoch_d += loss.item()
            loss.backward()
            optimizer_d.step()
        print(f"Epoch {epoch} , detector loss: {loss_epoch_d}")
        
        if epoch % 2 == 0 and (epoch > 30 or loss_epoch_d<0.1):
            torch.save(trigger, f'./poison_model/trigger_{epoch}.pth')
            
        # 2.3 测试
        # bd_attacker.detector.model.eval()
        # bd_attacker.generator.eval()
        # for data_flow in tqdm(loader_test.test_mask):
        #     target_node_ids = data_flow[0].n_id[:data_flow.batch_size]
        #     trigger = bd_attacker.generator(test_data.x, data_flow, train_phase=False)
        #     x_hidden_feature = bd_attacker.detector.model(test_data.x, data_flow, feature=True)[target_node_ids]
        #     testdata_mal_node_hidden_feature.append(x_hidden_feature)
    # # 3. 最终测试
    # bd_attacker.detector.model.eval()
    # bd_attacker.detector.test() 

def test_backdoor_attack(cfg, poison):
    # 1. 初始化
    print("训练+测试....")
    detector = Detector(cfg)
    cfg.label_map = detector.label_map
    cfg.feature_map = detector.feature_map
    cfg.feature_num = detector.feature_num
    cfg.mal_process_node = detector.malicious_node['SUBJECT_PROCESS']
    cfg.choosed_poisoned_nodes = choosed_poisoned_nodes
    
    # 处理自环
    edge_index = detector.train_data.edge_index
    edge_index, _ = remove_self_loops(edge_index)
    edge_index, _ = add_self_loops(edge_index, num_nodes=detector.train_data.num_nodes)
    detector.train_data.edge_index = edge_index
    
    self_loops = edge_index[:, edge_index[0] == edge_index[1]]
    unique_self_loops = self_loops.unique(dim=1)
    assert unique_self_loops.size(1) == detector.train_data.num_nodes, \
        f"Expected {detector.train_data.num_nodes} self-loops, got {unique_self_loops.size(1)}"

    edge_index = detector.test_data.edge_index
    edge_index, _ = remove_self_loops(edge_index)
    edge_index, _ = add_self_loops(edge_index, num_nodes=detector.test_data.num_nodes)
    detector.test_data.edge_index = edge_index
    
    if poison:
        addtrigger = AddTrigger(cfg)

        # 训练+测试
        loader_train0 = NeighborSampler(detector.train_data, size=[1.0, 1.0], num_hops=2, batch_size=cfg.batch_size, shuffle=False, add_self_loops=True)
        loader_test0 = NeighborSampler(detector.test_data, size=[1.0, 1.0], num_hops=2, batch_size=cfg.batch_size, shuffle=False, add_self_loops=True)
        
        edge_weight_train = torch.ones([detector.train_data.edge_index.shape[1]],device=cfg.device,dtype=torch.float)
        detector.train_data.edge_weight = edge_weight_train
        edge_weight_test = torch.ones([detector.test_data.edge_index.shape[1]],device=cfg.device,dtype=torch.float)
        detector.test_data.edge_weight = edge_weight_test
    
        train_data = detector.train_data.clone()
        test_data = detector.test_data.clone()
        # 先添加触发器
        for data_flow in loader_train0(torch.tensor(cfg.choosed_poisoned_nodes)):
            trigger = addtrigger.generator(detector.train_data.x, data_flow)
            train_data, _ = addtrigger(train_data, data_flow, trigger)
            # verifyTriggerAttached(bd_attacker.detector.train_data, train_data, data_flow.n_id, cfg.dataset)
            # print(sum(edge_weights_all)/data_flow.batch_size)
        for data_flow in loader_test0(torch.tensor(detector.malicious_node['SUBJECT_PROCESS'])):
            trigger = addtrigger.generator(detector.test_data.x, data_flow)
            test_data, _ = addtrigger(test_data, data_flow, trigger)
            # verifyTriggerAttached(bd_attacker.detector.test_data, test_data, data_flow.n_id, cfg.dataset)
            # print(sum(edge_weights_all)/data_flow.batch_size)
        print("已添加触发器")
        # 分离梯度
        train_data.x = train_data.x.detach()
        train_data.edge_index = train_data.edge_index.detach()
        train_data.edge_weight = train_data.edge_weight.detach()
        test_data.x = test_data.x.detach()
        test_data.edge_index = test_data.edge_index.detach()
        test_data.edge_weight = test_data.edge_weight.detach()

        # 正式数据
        loader_train = NeighborSampler(train_data, size=[1.0, 1.0], num_hops=2, batch_size=cfg.batch_size, shuffle=True, add_self_loops=True)
        loader_test = NeighborSampler(test_data, size=[1.0, 1.0], num_hops=2, batch_size=cfg.batch_size, shuffle=False, add_self_loops=True)
        print("训练中毒模型....")
        bd_attacker.detector.train_main(train_data, loader_train)
        print("测试中毒模型....")
        bd_attacker.detector.test_main(test_data, loader_test)
    else:
        print("训练正常模型....")
        bd_attacker.detector.train_main(bd_attacker.detector.train_data, loader_train)
        print("测试正常模型....")
        bd_attacker.detector.test_main(bd_attacker.detector.test_data, loader_test)
        
def verifyTriggerAttached(orig_data, poisoned_data, target_node, save_dir='trigger_verification'):
    """
    验证触发器是否正确添加到图上
    Args:
        orig_data: 原始图数据
        poisoned_data: 添加触发器后的图数据
        target_node: 目标节点索引
        save_dir: 保存验证图的目录
    """
    # 创建保存目录
    # target_node = random.sample(target_node.tolist(), 1)
    target_node = target_node[2].unsqueeze(0)
    os.makedirs(f'{save_dir}_verifyTriggerAttached', exist_ok=True)
    
    # 获取目标节点的两跳邻居
    def get_two_hop_neighbors(data, node_idx):
        edge_index = data.edge_index.cpu().numpy()
        neighbors = set()
        # 一跳邻居
        for i in range(edge_index.shape[1]):
            if edge_index[0, i] == node_idx:
                neighbors.add(edge_index[1, i])
            elif edge_index[1, i] == node_idx:
                neighbors.add(edge_index[0, i])

        # 两跳邻居
        two_hop_neighbors = set()
        for neighbor in neighbors:
            for i in range(edge_index.shape[1]):
                if edge_index[0, i] == neighbor:
                    two_hop_neighbors.add(edge_index[1, i])
                elif edge_index[1, i] == neighbor:
                    two_hop_neighbors.add(edge_index[0, i])
        return neighbors, two_hop_neighbors
    
    # 获取原始图和投毒图的子图
    orig_neighbors, orig_two_hop = get_two_hop_neighbors(orig_data, target_node)
    print('已获取原始子图,数量为', len(orig_neighbors), len(orig_two_hop))
    poison_neighbors, poison_two_hop = get_two_hop_neighbors(poisoned_data, target_node)
    print('已获取原始子图,数量为', len(poison_neighbors), len(poison_two_hop))
    
    # 创建子图
    def create_subgraph(data, node_idx, neighbors, two_hop_neighbors):
        nodes = set(node_idx) | neighbors | two_hop_neighbors
        subgraph = nx.MultiDiGraph()  # 使用有向多重图
        
        # 添加节点
        for node in nodes:
            node_type = 'Process' if data.y[node].item() == 0 else 'File'
            subgraph.add_node(node, node_type=node_type)
        
        # 添加边
        edge_index = data.edge_index.cpu().numpy()
        edge_weight = data.edge_weight.detach().cpu().numpy() if hasattr(data, 'edge_weight') else None
        
        for i in range(edge_index.shape[1]):
            src, dst = edge_index[0, i], edge_index[1, i]
            if src in nodes and dst in nodes:
                weight = edge_weight[i] if edge_weight is not None else 1.0
                if weight > 0:  # 只添加权重大于0的边 ！！！！！！！！！！
                    subgraph.add_edge(src, dst, weight=weight)
        
        return subgraph
    
    # 创建并保存子图
    orig_subgraph = create_subgraph(orig_data, target_node, orig_neighbors, orig_two_hop)
    poison_subgraph = create_subgraph(poisoned_data, target_node, poison_neighbors, poison_two_hop)
    
    # 绘制并保存图
    def draw_and_save_graph(G, filename, title):
        plt.figure(figsize=(15, 10))
        pos = nx.spring_layout(G, k=1, iterations=50)
        
        # 绘制节点
        process_nodes = [n for n, d in G.nodes(data=True) if d['node_type'] == 'Process']
        file_nodes = [n for n, d in G.nodes(data=True) if d['node_type'] == 'File']
        
        nx.draw_networkx_nodes(G, pos, nodelist=process_nodes, node_color='lightblue', 
                             node_size=500, label='Process')
        nx.draw_networkx_nodes(G, pos, nodelist=file_nodes, node_color='lightgreen', 
                             node_size=500, label='File')
        
        # 特别标记目标节点
        nx.draw_networkx_nodes(G, pos, nodelist=target_node, node_color='red', 
                             node_size=700, label='Target Node')
        
        # 绘制边和标签
        # 绘制边
        edge_colors = []
        edge_widths = []
        edge_labels = {}
        
        for (u, v, key, data) in G.edges(data=True, keys=True):
            weight = data.get('weight', 1.0)
            if weight > 0:  # 只显示权重大于0的边
                edge_colors.append('blue')
                edge_widths.append(weight * 2)  # 根据权重调整边的宽度
                edge_labels[(u, v)] = f"{weight:.2f}"
        
        # 绘制边
        nx.draw_networkx_edges(G, pos, edge_color=edge_colors, width=edge_widths, 
                              arrows=True, arrowsize=20, connectionstyle='arc3,rad=0.2')
        
        # 绘制节点标签
        nx.draw_networkx_labels(G, pos)
        
        # 绘制边标签
        nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=8)
        
        plt.title(title)
        plt.legend()
        plt.savefig(os.path.join(f'{save_dir}_verifyTriggerAttached', filename), dpi=300, bbox_inches='tight')
        plt.close()
    
    # # 保存原始图和投毒图
    # draw_and_save_graph(orig_subgraph, f'orig_graph_node_{target_node}.png', 
    #                    f'Original Graph around Node {target_node}')
    # draw_and_save_graph(poison_subgraph, f'poisoned_graph_node_{target_node}.png', 
    #                    f'Poisoned Graph around Node {target_node}')
    
    # 打印统计信息
    print(f"\n验证节点 {target_node} 的触发器添加情况:")
    print(f"原始图: {len(orig_subgraph.nodes())} 个节点, {len(orig_subgraph.edges())} 条边")
    print(f"投毒图: {len(poison_subgraph.nodes())} 个节点, {len(poison_subgraph.edges())} 条边")
    print(f"新增节点数: {len(poison_subgraph.nodes()) - len(orig_subgraph.nodes())}")
    print(f"新增边数: {len(poison_subgraph.edges()) - len(orig_subgraph.edges())}")
    
    # 打印边的权重信息
    print("\n原始图边权重统计:")
    orig_weights = [d.get('weight', 1.0) for (u, v, k, d) in orig_subgraph.edges(data=True, keys=True)]
    if orig_weights:
        print(f"  最小权重: {min(orig_weights):.4f}")
        print(f"  最大权重: {max(orig_weights):.4f}")
        print(f"  平均权重: {sum(orig_weights)/len(orig_weights):.4f}")
    
    print("\n投毒图边权重统计:")
    poison_weights = [d.get('weight', 1.0) for (u, v, k, d) in poison_subgraph.edges(data=True, keys=True)]
    if poison_weights:
        print(f"  最小权重: {min(poison_weights):.4f}")
        print(f"  最大权重: {max(poison_weights):.4f}")
        print(f"  平均权重: {sum(poison_weights)/len(poison_weights):.4f}")
    
    
    
    
    