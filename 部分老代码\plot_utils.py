from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import torch
import numpy as np
import umap
from sklearn.preprocessing import StandardScaler
import os
from sklearn.manifold import TSNE
from sklearn.neighbors import NearestNeighbors
from kneed import KneeLocator

def plot_embeddings2(train_hidden_feature, x_test):
    # 合并训练和测试数据
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))  # 0: train, 1: test

    # PCA降维到2D
    pca = PCA(n_components=2)
    embeddings_2d = pca.fit_transform(all_embeddings)

    # 可视化
    plt.figure(figsize=(10, 6))
    plt.scatter(embeddings_2d[labels == 0, 0], embeddings_2d[labels == 0, 1], 
                c='blue', alpha=0.5, s=2, label='Train (52,887 samples)')
    plt.scatter(embeddings_2d[labels == 1, 0], embeddings_2d[labels == 1, 1], 
                c='red', alpha=0.8, marker='X', s=100, label='Test (23 samples)')
    plt.title('PCA Projection of Embeddings')
    plt.legend()
    plt.show()

def plot_embeddings_local(train_hidden_feature, x_test, epoch, choosed_poisoned_nodes, n_std=10):
    # 合并数据并降维
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))
    
    pca = PCA(n_components=2)
    embeddings_2d = pca.fit_transform(all_embeddings)
    
    # 提取测试点坐标
    test_points = embeddings_2d[labels == 1]
    test_mean = test_points.mean(axis=0)  # 测试点中心
    test_std = test_points.std(axis=0)    # 测试点标准差
    
    # 计算筛选边界
    x_min, x_max = test_mean[0] - n_std * test_std[0], test_mean[0] + n_std * test_std[0]
    y_min, y_max = test_mean[1] - n_std * test_std[1], test_mean[1] + n_std * test_std[1]
    
    # 筛选训练点（仅保留边界内的点）
    train_mask = (
        (embeddings_2d[labels == 0, 0] >= x_min) & 
        (embeddings_2d[labels == 0, 0] <= x_max) & 
        (embeddings_2d[labels == 0, 1] >= y_min) & 
        (embeddings_2d[labels == 0, 1] <= y_max)
    )
    poisoned_mask = np.zeros(len(train_hidden_feature), dtype=bool)
    for _,value in choosed_poisoned_nodes.items():
        poisoned_mask[value] = True
    
    filtered_train_clean = embeddings_2d[labels == 0][train_mask & ~poisoned_mask]
    filtered_train_poisoned = embeddings_2d[labels == 0][train_mask & poisoned_mask]
    
    # 可视化局部区域
    plt.figure(figsize=(10, 6))
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean (filtered: {len(filtered_train_clean)}/{len(train_hidden_feature)})'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.8, s=40, 
        label=f'Train poisoned (filtered: {len(filtered_train_poisoned)}/{len(train_hidden_feature)})',
        edgecolors='black', linewidth=1.5, zorder=2  # 边框
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='red', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)'
    )
    
    # 标记测试区域边界
    plt.plot(
        [x_min, x_max, x_max, x_min, x_min],
        [y_min, y_min, y_max, y_max, y_min],
        'k--', alpha=0.3, label=f'Test Region (±{n_std}σ)'
    )
    
    plt.title('PCA Projection (Local Region Near Test Points)')
    plt.legend()
    # plt.show()
    if not os.path.exists('./poison_model/embedding/local'):
        os.makedirs('./poison_model/embedding/local')
    filename = f"embedding_local_epoch{epoch}.png" if epoch is not None else f"embedding_local.png"
    save_path = os.path.join('./poison_model/embedding/local', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=150)
    plt.close()

def plot_embeddings_global(train_hidden_feature, x_test, epoch, choosed_poisoned_nodes, n_std=10):
    # 合并数据并降维
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))
    
    pca = PCA(n_components=2)
    embeddings_2d = pca.fit_transform(all_embeddings)
    
    # 提取测试点坐标
    test_points = embeddings_2d[labels == 1]

    poisoned_mask = np.zeros(len(train_hidden_feature), dtype=bool)
    for _,value in choosed_poisoned_nodes.items():
        poisoned_mask[value] = True
    
    filtered_train_clean = embeddings_2d[labels == 0][~poisoned_mask]
    filtered_train_poisoned = embeddings_2d[labels == 0][poisoned_mask]
    
    plt.figure(figsize=(10, 6))
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean: {len(filtered_train_clean)}/{len(train_hidden_feature)}'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.8, s=40, 
        label=f'Train poisoned: {len(filtered_train_poisoned)}/{len(train_hidden_feature)}',
        edgecolors='black', linewidth=1.5, zorder=2  # 边框
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='red', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)'
    )
    plt.title('PCA Projection (Global Region)')
    plt.legend()
    # plt.show()
    filename = f"embedding_global_epoch{epoch}.png" if epoch is not None else f"embedding_global.png"
    if not os.path.exists('./poison_model/embedding/global'):
        os.makedirs('./poison_model/embedding/global')
    save_path = os.path.join('./poison_model/embedding/global', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=150)
    plt.close()

def plot_embeddings_local_umap(train_hidden_feature, x_test, epoch, choosed_poisoned_nodes, n_std=10):
    """
    使用UMAP降维可视化隐藏特征（局部区域）
    
    参数:
        train_hidden_feature: 训练集隐藏特征 (Tensor)
        x_test: 测试集隐藏特征 (Tensor)
        epoch: 当前epoch（用于文件名）
        choosed_poisoned_nodes: 投毒节点字典 {index: node_id}
        n_std: 测试点区域边界标准差倍数 (默认3σ)
    """
    # 合并数据并标准化
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    scaler = StandardScaler()
    all_embeddings = scaler.fit_transform(all_embeddings)  # UMAP对尺度敏感
    
    # UMAP降维
    reducer = umap.UMAP(n_components=2, random_state=42, n_neighbors=15, min_dist=0.1)
    embeddings_2d = reducer.fit_transform(all_embeddings)
    
    # 标签处理
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))
    test_points = embeddings_2d[labels == 1]
    test_mean = test_points.mean(axis=0)
    test_std = test_points.std(axis=0)
    
    # 计算筛选边界
    x_min, x_max = test_mean[0] - n_std * test_std[0], test_mean[0] + n_std * test_std[0]
    y_min, y_max = test_mean[1] - n_std * test_std[1], test_mean[1] + n_std * test_std[1]
    
    # 生成掩码
    train_mask = (
        (embeddings_2d[labels == 0, 0] >= x_min) & 
        (embeddings_2d[labels == 0, 0] <= x_max) & 
        (embeddings_2d[labels == 0, 1] >= y_min) & 
        (embeddings_2d[labels == 0, 1] <= y_max)
    )
    
    poisoned_mask = np.zeros(len(train_hidden_feature), dtype=bool)
    for node_id in choosed_poisoned_nodes.values():
        poisoned_mask[node_id] = True
    
    # 筛选数据
    filtered_train_clean = embeddings_2d[labels == 0][train_mask & ~poisoned_mask]
    filtered_train_poisoned = embeddings_2d[labels == 0][train_mask & poisoned_mask]
    
    # 可视化
    plt.figure(figsize=(12, 8))
    
    # 绘制测试点区域背景
    plt.fill_between(
        [x_min, x_max], y_min, y_max,
        color='orange', alpha=0.05, label=f'Test Region (±{n_std}σ)'
    )
    
    # 绘制点集
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1],
        c='blue', alpha=0.6, s=8, edgecolor='w', linewidth=0.3,
        label=f'Clean Train ({len(filtered_train_clean)}/{len(train_hidden_feature)})'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1],
        c='red', alpha=0.8, s=12, marker='*', edgecolor='k', linewidth=0.5,
        label=f'Poisoned Train ({len(filtered_train_poisoned)})'
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1],
        c='lime', alpha=0.9, marker='X', s=50, edgecolor='k',
        label=f'Test ({len(x_test)})'
    )
    
    # 标注统计信息
    plt.text(
        0.05, 0.95,
        f'Poisoned/Clean Ratio: {len(filtered_train_poisoned)}/{len(filtered_train_clean)} = {len(filtered_train_poisoned)/len(filtered_train_clean):.1%}',
        transform=plt.gca().transAxes, ha='left', va='top',
        bbox=dict(facecolor='white', alpha=0.7)
    )
    
    # 美化图形
    plt.title(f'UMAP Projection (Epoch {epoch})' if epoch else 'UMAP Projection')
    plt.legend(loc='upper right', framealpha=0.7)
    plt.grid(True, alpha=0.2)
    
    # 保存结果
    os.makedirs('./poison_model/embedding/local_umap', exist_ok=True)
    filename = f"umap_local_epoch{epoch}.png" if epoch else "umap_local.png"
    plt.savefig(
        f'./poison_model/embedding/local_umap/{filename}',
        dpi=200, bbox_inches='tight', pad_inches=0.1
    )
    plt.close()
    
def plot_embeddings_tsne(train_hidden_feature, x_test, epoch, choosed_poisoned_nodes, n_std=100):
    # 合并数据并降维
    all_embeddings = torch.cat([train_hidden_feature, x_test], dim=0).numpy()
    labels = np.array([0] * len(train_hidden_feature) + [1] * len(x_test))
    
    # 使用 t-SNE 降维到 2D
    tsne = TSNE(n_components=2, random_state=42, perplexity=20, n_iter=1000)
    embeddings_2d = tsne.fit_transform(all_embeddings)
    
    # 提取测试点坐标
    test_points = embeddings_2d[labels == 1]
    test_mean = test_points.mean(axis=0)  # 测试点中心
    test_std = test_points.std(axis=0)    # 测试点标准差
    
    # 计算筛选边界
    x_min, x_max = min(test_points[:, 0]) - 3, max(test_points[:, 0]) + 3
    y_min, y_max = min(test_points[:, 1]) - 3, max(test_points[:, 1]) + 3
    
    poisoned_mask = np.zeros(len(train_hidden_feature), dtype=bool)
    for _, value in choosed_poisoned_nodes.items():
        poisoned_mask[value] = True
    
    filtered_train_clean = embeddings_2d[labels == 0][~poisoned_mask]
    filtered_train_poisoned = embeddings_2d[labels == 0][poisoned_mask]
    
    plt.figure(figsize=(10, 6))
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.xlim(x_min, x_max)  # x轴范围
    plt.ylim(y_min, y_max)  # y轴范围
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean: {len(filtered_train_clean)}/{len(train_hidden_feature)}',
        zorder=1
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.5, s=10,
        label=f'Train poisoned: {len(filtered_train_poisoned)}/{len(train_hidden_feature)}',
        edgecolors='black', linewidth=1.5,# 边框
        zorder=2
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='lime', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)',
        zorder=3
    )
    plt.title('t-SNE Projection (local Region)')
    plt.legend()
    # plt.show()
    filename = f"embedding_local_epoch{epoch}.png" if epoch is not None else f"embedding_local.png"
    if not os.path.exists('./poison_model/embedding/local'):
        os.makedirs('./poison_model/embedding/local')
    save_path = os.path.join('./poison_model/embedding/local', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=300)
    plt.close()
    
    # ============global==================
    
    plt.figure(figsize=(10, 6))
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean: {len(filtered_train_clean)}/{len(train_hidden_feature)}'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.5, s=10, 
        label=f'Train poisoned: {len(filtered_train_poisoned)}/{len(train_hidden_feature)}',
        edgecolors='black', linewidth=1.5, zorder=2  # 边框
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='lime', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)'
    )
    plt.title('t-SNE Projection (Global Region)')
    plt.legend()
    # plt.show()
    filename = f"embedding_global_epoch{epoch}.png" if epoch is not None else f"embedding_global.png"
    if not os.path.exists('./poison_model/embedding/global'):
        os.makedirs('./poison_model/embedding/global')
    save_path = os.path.join('./poison_model/embedding/global', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=300)
    plt.close()
    
    # ========= level 2 ====================
    plt.figure(figsize=(10, 6))
    plt.xlim(test_mean[0]-25, test_mean[0]+25)  # x轴范围
    plt.ylim(test_mean[1]-40, test_mean[1]+40)  # y轴范围
    plt.scatter(
        filtered_train_clean[:, 0], filtered_train_clean[:, 1], 
        c='blue', alpha=0.5, s=10, 
        label=f'Train clean: {len(filtered_train_clean)}/{len(train_hidden_feature)}'
    )
    plt.scatter(
        filtered_train_poisoned[:, 0], filtered_train_poisoned[:, 1], 
        c='red', alpha=0.5, s=10, 
        label=f'Train poisoned: {len(filtered_train_poisoned)}/{len(train_hidden_feature)}',
        edgecolors='black', linewidth=1.5, zorder=2  # 边框
    )
    plt.scatter(
        test_points[:, 0], test_points[:, 1], 
        c='lime', alpha=0.8, marker='X', s=30, 
        label=f'Test ({len(x_test)} samples)'
    )
    plt.title('t-SNE Projection (Global Region)')
    plt.legend()
    # plt.show()
    filename = f"embedding_l2_epoch{epoch}.png" if epoch is not None else f"embedding_l2.png"
    if not os.path.exists('./poison_model/embedding/l2'):
        os.makedirs('./poison_model/embedding/l2')
    save_path = os.path.join('./poison_model/embedding/l2', filename)
    plt.savefig(save_path, bbox_inches='tight', dpi=300)
    plt.close()
    
def visualize_clusters(all_features, all_labels, n_train):
    """
    改进的可视化函数：直接根据聚类标签绘制
    
    参数:
        all_features: 所有节点的特征矩阵
        all_labels: 所有节点的簇标签数组
        n_train: 训练节点数量
    """
    import matplotlib.pyplot as plt
    import umap
    
    # 降维可视化
    reducer = umap.UMAP(n_components=2, random_state=42)
    embeddings_2d = reducer.fit_transform(all_features)
    
    # 分离训练和测试节点
    train_2d = embeddings_2d[:n_train]
    test_2d = embeddings_2d[n_train:]
    train_labels = all_labels[:n_train]
    test_labels = all_labels[n_train:]

    # 准备颜色映射
    unique_labels = set(all_labels) - {-1}  # 排除噪声点
    colors = plt.cm.tab20(np.linspace(0, 1, len(unique_labels)))
    color_map = {label: colors[i] for i, label in enumerate(unique_labels)}

    plt.figure(figsize=(12, 8))
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 绘制训练集噪声点
    noise_mask = (train_labels == -1)
    if np.any(noise_mask):
        plt.scatter(train_2d[:, 0], train_2d[:, 1],
                   c='gray', marker='.', s=8, alpha=0.2,
                   label='Noise',)
    # 绘制所有训练节点（按簇分组）
    for label in unique_labels:
        mask = (train_labels == label)
        plt.scatter(train_2d[mask, 0], train_2d[mask, 1],
                   c=[color_map[label]], marker='o', s=20, alpha=0.6,
                   label=f'Cluster {label}')

    # 绘制测试集噪声点
    noise_mask = (test_labels == -1)
    if np.any(noise_mask):
        plt.scatter(test_2d[:, 0], test_2d[:, 1],
                   c='gray', marker='s', s=100, edgecolor='black', linewidth=1.5, alpha=0.8,
                   label='Noise')
    # 绘制测试节点（保持簇颜色，用x标记）
    for label in unique_labels:
        mask = (test_labels == label)
        if np.any(mask):
            plt.scatter(test_2d[mask, 0], test_2d[mask, 1],
                       c=[color_map[label]], marker='s', s=100,
                       edgecolor='black', linewidth=1.5, alpha=0.8)


    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.title('DBSCAN聚类可视化\n(相同颜色=同一簇，x=恶意节点)')
    plt.xlabel('UMAP维度1')
    plt.ylabel('UMAP维度2')
    plt.tight_layout()
    plt.show()

def find_optimal_eps(X, k=None, plot=True):
    """
    通过k-distance图确定DBSCAN的最佳Eps参数
    
    参数:
        X: 输入数据 (n_samples, n_features)
        k: 近邻数 (默认自动计算为 2*特征数-1)
        plot: 是否绘制k-distance图 (默认True)
        
    返回:
        optimal_eps: 推荐的Eps值
        knee_point: 拐点位置
    """
    # 1. 自动确定k值
    if k is None:
        k = 2 * X.shape[1] - 1
        print(f"自动设置k值为: {k} (2*特征数-1)")
    
    # 2. 计算k-distance
    neigh = NearestNeighbors(n_neighbors=k)
    neigh.fit(X)
    distances, _ = neigh.kneighbors(X)
    k_distances = distances[:, -1]  # 每个点到第k近邻的距离
    sorted_k_distances = np.sort(k_distances)[::-1]  # 从大到小排序
    
    # 3. 寻找拐点
    kneedle = KneeLocator(
        x=np.arange(len(sorted_k_distances)),
        y=sorted_k_distances,
        curve='convex',
        direction='decreasing',
    )
    optimal_eps = kneedle.knee_y
    knee_point = kneedle.knee
    
    # 4. 可视化
    if plot:
        plt.figure(figsize=(10, 6))
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        plt.plot(sorted_k_distances, 'b-', linewidth=2, label='k-distance曲线')
        plt.axhline(y=optimal_eps, color='r', linestyle='--', 
                   label=f'推荐Eps值: {optimal_eps:.2f}')
        plt.scatter(knee_point, optimal_eps, c='red', s=100, zorder=5)
        
        # 标注区域
        plt.axhspan(0, optimal_eps, facecolor='green', alpha=0.1, 
                   label='簇区域')
        plt.axhspan(optimal_eps, sorted_k_distances[0], facecolor='red', 
                   alpha=0.1, label='噪声区域')
        
        # 图表装饰
        plt.title('k-distance图 (k = {})'.format(k), pad=20)
        plt.xlabel('Objects (按距离排序)', fontsize=12)
        plt.ylabel('{}-distance'.format(k), fontsize=12)
        plt.legend()
        plt.grid(alpha=0.3)
        plt.tight_layout()
        plt.show()
    print(f"optimal_eps: {optimal_eps}")
    return optimal_eps, knee_point