def similarity_analyse(x_train, train_process_orig_idx, x_test, test_process_orig_idx, k, visualize=True):
    """
    计算与测试节点（恶意样本）最近的k个训练节点。
    
    参数:
    - x_train: 训练集的隐特征 (numpy array, shape: [n_train_nodes, feature_dim])
    - train_process_orig_idx: 训练集中每个图的SUBJECT_PROCESS节点原始索引列表(list[array,array,array...])
    - x_test: 测试集的隐特征 (numpy array, shape: [n_test_nodes, feature_dim])
    - test_process_orig_idx: 测试集中SUBJECT_PROCESS节点原始索引 (numpy array)
    
    返回:
    - similarity: 字典，键为测试节点索引，值为其最近k个训练节点的原始索引列表
    """
    global_train_process_idx = np.arange(sum([len(idx) for idx in train_process_orig_idx]))
    
    # 计算训练集和测试集之间的距离矩阵
    dist_matrix = distance_matrix(x_test, x_train)

    # 初始化输出字典
    similarity = {}
    nearest_k_distances_mean_all = []
    for i, test_idx in enumerate(test_process_orig_idx):
        distances = dist_matrix[i]
        sorted_indices = np.argsort(distances)
        nearest_k_indices = sorted_indices[:k]
        nearest_k_distances_mean = np.mean(distances[nearest_k_indices])
        nearest_k_distances_mean_all.append(nearest_k_distances_mean)
    
        nearest_global_indices = global_train_process_idx[nearest_k_indices]
        nearest_orig_indices_by_graph = [[] for _ in range(len(train_process_orig_idx))]

        # 还原到按图分开的原始索引
        start_idx = 0
        for graph_idx, orig_idx_list in enumerate(train_process_orig_idx):
            end_idx = start_idx + len(orig_idx_list)
            # 找到属于当前图的全局索引
            mask = (nearest_global_indices >= start_idx) & (nearest_global_indices < end_idx)
            if np.any(mask):
                # 映射回当前图的原始索引
                graph_nearest_indices = nearest_global_indices[mask] - start_idx
                orig_indices = [int(orig_idx_list[idx]) for idx in graph_nearest_indices]
                nearest_orig_indices_by_graph[graph_idx] = orig_indices
            start_idx = end_idx

        # 存储结果
        similarity[int(test_idx)] = nearest_orig_indices_by_graph
    print(nearest_k_distances_mean_all)
        
    train_orig_idx_map = []
    for graph_idx, orig_idx_list in enumerate(train_process_orig_idx):
        for orig_idx in orig_idx_list:
            train_orig_idx_map.append((graph_idx, orig_idx))
            
    if visualize:
        all_features = np.concatenate([x_train, x_test], axis=0)
        test_indices = np.arange(len(x_train), len(x_train) + len(x_test))  # 测试节点的全局索引
        for i, test_idx in enumerate(test_process_orig_idx):
            highlight_groups = []
            
            highlight_groups.append({
                "indices": [test_indices[i]],
                "label": "测试节点 (恶意样本)",
                "color": "red",
                "marker": "s",  # 方形标记
                "size": 100,
                "alpha": 1.0,
                "edgecolor": "black",
                "linewidths": 1.5,
            })
            nearest_indices = []
            for graph_idx, indices in enumerate(similarity[int(test_idx)]):
                # 将每个图的原始索引映射回全局索引
                for orig_idx in indices:
                    for global_idx, (g_idx, o_idx) in enumerate(train_orig_idx_map):
                        if g_idx == graph_idx and o_idx == orig_idx:
                            nearest_indices.append(global_idx)
            
            if nearest_indices:  # 仅当存在最近邻节点时添加
                highlight_groups.append({
                    "indices": nearest_indices,
                    "label": f"测试节点 {test_idx} 的最近 {k} 个节点",
                    "color": plt.cm.tab10(i % 10),  # 使用不同颜色区分不同测试节点的最近邻
                    "marker": "o",
                    "size": 50,
                    "alpha": 0.8,
                    "edgecolor": "black",
                    "linewidths": 1.0,
                })
            background_params = {
                "color": "lightgray",
                "alpha": 0.2,
                "size": 10,
                "label": "背景节点"
            }
            visualize_umap(
                features=all_features,
                highlight_groups=highlight_groups,
                background_params=background_params,
                title=f"UMAP 可视化 - 测试节点及其最近 {k} 个训练节点",
                random_state=42,
                n_neighbors=15,  # UMAP 参数，可根据需要调整
                min_dist=0.1
            )
    return similarity

def get_similarity_nodes(cfg, k, visualize):
    print('相似性度量...')
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)  # type 2 idx
    
    args = build_args()
    metadata = load_metadata(cfg.dataset)  # 将原图数据的节点和边进行onehot嵌入，并将每个图分开保存成pkl
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    args.num_hidden = 64
    args.max_epoch = 50
    args.num_layers = 3
    
    model = build_model(args)
    model = model.to(cfg.device)    
    model.load_state_dict(torch.load("./checkpoints/checkpoint-{}.pt".format(cfg.dataset), map_location=cfg.device))
    
    malicious, _ = metadata['malicious']
    n_train = metadata['n_train']
    n_test = metadata['n_test']
        
    with torch.no_grad():
        x_train = []
        train_process_orig_idx = []
        for i in range(n_train):
            g = load_entity_level_dataset(cfg.dataset, 'train', i).to(cfg.device)
            process_mask = (g.ndata['type']==cfg.node_type_dict['SUBJECT_PROCESS']).nonzero().squeeze()
            x_train.append(model.embed(g)[process_mask].cpu().numpy())
            train_process_orig_idx.append(process_mask.numpy())
            del g
        x_train = np.concatenate(x_train, axis=0)
        
        x_test = []
        test_process_orig_idx = []
        for i in range(n_test):
            g = load_entity_level_dataset(cfg.dataset, 'test', i).to(cfg.device)
            
            mal_mask = torch.zeros(g.number_of_nodes(), dtype=torch.bool)
            mal_mask[malicious] = 1
            process_mask = ((g.ndata['type']==cfg.node_type_dict['SUBJECT_PROCESS'])&mal_mask).nonzero().squeeze()
            x_test.append(model.embed(g)[process_mask].cpu().numpy())
            test_process_orig_idx.append(process_mask.numpy())
            del g
        x_test = np.concatenate(x_test, axis=0)

    similarity = similarity_analyse(x_train,train_process_orig_idx,x_test, test_process_orig_idx[0], k=k, visualize=visualize)

    with open(f'./data/{cfg.dataset}/similarity_{k}.json', 'w', encoding='utf-8') as f:
        json.dump(similarity, f)